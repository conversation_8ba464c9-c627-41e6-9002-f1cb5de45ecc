<script setup lang="ts">
import type { NodeConfig } from '../types/workflow';

import { message } from 'ant-design-vue';
import {
  CirclePlay,
  CircleStop,
  GitBranch,
  GitFork,
  Merge,
  UserCheck,
} from 'lucide-vue-next';

interface Props {
  nodeConfigs: Array<{
    category: string;
    nodes: NodeConfig[];
  }>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  addNode: [nodeType: string, position?: { x: number; y: number }];
  dragNode: [nodeType: string];
}>();

// 节点图标映射
const nodeIconMap = {
  start: CirclePlay,
  end: CircleStop,
  approval: UserCheck,
  condition: GitBranch,
  parallel: GitFork,
  merge: Merge,
};

// 拖拽相关
const handleDragStart = (e: DragEvent, nodeType: string) => {
  // 使用 LogicFlow 的拖拽 API
  emit('dragNode', nodeType);

  // 阻止默认的 HTML5 拖拽行为
  e.preventDefault();
};

// 双击添加节点到画布中心
const handleDoubleClick = (nodeType: string) => {
  emit('addNode', nodeType);
  message.success('节点已添加到画布中心');
};
</script>

<template>
  <div class="workflow-node-panel">
    <div class="panel-header">
      <h3>节点面板</h3>
    </div>

    <!-- 节点列表 -->
    <div class="panel-content">
      <div
        v-for="category in nodeConfigs"
        :key="category.category"
        class="node-category"
      >
        <div class="category-title">{{ category.category }}</div>
        <div class="node-list">
          <div
            v-for="node in category.nodes"
            :key="node.type"
            class="node-item"
            draggable="true"
            @dragstart="handleDragStart($event, node.type)"
            @dblclick="handleDoubleClick(node.type)"
            @mousedown="emit('dragNode', node.type)"
          >
            <div
              class="node-icon"
              :style="{ backgroundColor: node.color || '#1890ff' }"
            >
              <component :is="nodeIconMap[node.type]" class="h-4 w-4" />
            </div>
            <div class="node-info">
              <div class="node-title">{{ node.name }}</div>
              <div class="node-description">{{ node.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.workflow-node-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #f0f0f0;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
}

.panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.node-category {
  margin-bottom: 16px;
}

.category-title {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: move;
  user-select: none;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.node-item:hover {
  background-color: #f8f9fa;
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgb(24 144 255 / 10%);
  transform: translateY(-1px);
}

.node-item:active {
  box-shadow: 0 1px 4px rgb(24 144 255 / 15%);
  transform: translateY(0);
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  color: #fff;
  border-radius: 4px;
}

.node-info {
  flex: 1;
}

.node-title {
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.node-description {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  line-height: 1.4;
  color: #8c8c8c;
  white-space: nowrap;
}
</style>
