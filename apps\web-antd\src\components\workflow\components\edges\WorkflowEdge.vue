<template>
  <g
    :class="edgeClasses"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @contextmenu="handleContextMenu"
  >
    <!-- 连线路径 -->
    <path
      :d="edgePath"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      :stroke-dasharray="strokeDasharray"
      fill="none"
      :class="pathClasses"
    />

    <!-- 箭头标记 -->
    <defs v-if="showArrow">
      <marker
        :id="`arrow-${edge.id}`"
        markerWidth="10"
        markerHeight="10"
        refX="8"
        refY="3"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <path
          d="M0,0 L0,6 L9,3 z"
          :fill="strokeColor"
        />
      </marker>
    </defs>

    <!-- 动画路径（如果启用动画） -->
    <path
      v-if="edge.properties.animated"
      :d="edgePath"
      stroke="rgba(255, 255, 255, 0.8)"
      stroke-width="2"
      fill="none"
      stroke-dasharray="4 4"
      :class="animatedPathClasses"
    />

    <!-- 连线标签 -->
    <g v-if="edge.properties.label" :transform="labelTransform">
      <rect
        :x="labelRect.x"
        :y="labelRect.y"
        :width="labelRect.width"
        :height="labelRect.height"
        :fill="labelBackgroundColor"
        :stroke="labelBorderColor"
        rx="4"
        ry="4"
        class="workflow-edge-label-bg"
      />
      <text
        :x="labelPosition.x"
        :y="labelPosition.y"
        :fill="labelTextColor"
        text-anchor="middle"
        dominant-baseline="middle"
        :font-size="labelFontSize"
        class="workflow-edge-label-text"
      >
        {{ edge.properties.label }}
      </text>
    </g>

    <!-- 选择指示器 -->
    <path
      v-if="selected"
      :d="edgePath"
      stroke="rgba(24, 144, 255, 0.4)"
      :stroke-width="strokeWidth + 6"
      fill="none"
      class="workflow-edge-selection"
    />

    <!-- 悬停区域（增加点击区域） -->
    <path
      :d="edgePath"
      stroke="transparent"
      :stroke-width="Math.max(strokeWidth + 10, 20)"
      fill="none"
      class="workflow-edge-hover-area"
    />
  </g>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { WorkflowEdge, WorkflowNode } from '../../types';
import { getEdgePath } from '../../utils';

interface Props {
  edge: WorkflowEdge;
  sourceNode: WorkflowNode;
  targetNode: WorkflowNode;
  selected?: boolean;
  scale?: number;
}

interface Emits {
  (e: 'click', edge: WorkflowEdge, event: MouseEvent): void;
  (e: 'double-click', edge: WorkflowEdge, event: MouseEvent): void;
  (e: 'context-menu', edge: WorkflowEdge, event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  scale: 1,
});

const emit = defineEmits<Emits>();

// 连线样式类
const edgeClasses = computed(() => [
  'workflow-edge',
  `workflow-edge-${props.edge.type}`,
  {
    'workflow-edge-selected': props.selected,
    'workflow-edge-animated': props.edge.properties.animated,
  },
]);

const pathClasses = computed(() => [
  'workflow-edge-path',
  {
    'workflow-edge-path-selected': props.selected,
  },
]);

const animatedPathClasses = computed(() => [
  'workflow-edge-animated-path',
]);

// 计算连线路径
const edgePath = computed(() => {
  const { sourceNode, targetNode, edge } = props;
  
  // 获取源节点和目标节点的中心点
  const sourceSize = sourceNode.size || { width: 120, height: 60 };
  const targetSize = targetNode.size || { width: 120, height: 60 };
  
  const sourceX = sourceNode.position.x + sourceSize.width / 2;
  const sourceY = sourceNode.position.y + sourceSize.height / 2;
  const targetX = targetNode.position.x + targetSize.width / 2;
  const targetY = targetNode.position.y + targetSize.height / 2;

  // 根据连线类型和节点位置计算连接点
  let sourcePosition = 'right';
  let targetPosition = 'left';

  // 简单的连接点计算：根据节点相对位置确定连接点
  if (sourceX < targetX) {
    sourcePosition = 'right';
    targetPosition = 'left';
  } else if (sourceX > targetX) {
    sourcePosition = 'left';
    targetPosition = 'right';
  } else if (sourceY < targetY) {
    sourcePosition = 'bottom';
    targetPosition = 'top';
  } else {
    sourcePosition = 'top';
    targetPosition = 'bottom';
  }

  // 调整连接点到边界
  let finalSourceX = sourceX;
  let finalSourceY = sourceY;
  let finalTargetX = targetX;
  let finalTargetY = targetY;

  switch (sourcePosition) {
    case 'right':
      finalSourceX = sourceNode.position.x + sourceSize.width;
      break;
    case 'left':
      finalSourceX = sourceNode.position.x;
      break;
    case 'bottom':
      finalSourceY = sourceNode.position.y + sourceSize.height;
      break;
    case 'top':
      finalSourceY = sourceNode.position.y;
      break;
  }

  switch (targetPosition) {
    case 'right':
      finalTargetX = targetNode.position.x + targetSize.width;
      break;
    case 'left':
      finalTargetX = targetNode.position.x;
      break;
    case 'bottom':
      finalTargetY = targetNode.position.y + targetSize.height;
      break;
    case 'top':
      finalTargetY = targetNode.position.y;
      break;
  }

  return getEdgePath(
    edge.type,
    finalSourceX,
    finalSourceY,
    finalTargetX,
    finalTargetY,
    sourcePosition,
    targetPosition
  );
});

// 连线样式
const strokeColor = computed(() => {
  return props.edge.properties.color || '#8c8c8c';
});

const strokeWidth = computed(() => {
  return props.edge.properties.strokeWidth || 2;
});

const strokeDasharray = computed(() => {
  // 可以根据连线类型或属性设置虚线样式
  return props.edge.properties.dashed ? '5,5' : 'none';
});

const showArrow = computed(() => {
  return props.edge.markers?.end === 'arrow' || true; // 默认显示箭头
});

// 标签相关计算
const labelPosition = computed(() => {
  if (!props.edge.properties.label) {
    return { x: 0, y: 0 };
  }

  // 计算标签在连线中点的位置
  const { sourceNode, targetNode } = props;
  const sourceSize = sourceNode.size || { width: 120, height: 60 };
  const targetSize = targetNode.size || { width: 120, height: 60 };
  
  const sourceX = sourceNode.position.x + sourceSize.width / 2;
  const sourceY = sourceNode.position.y + sourceSize.height / 2;
  const targetX = targetNode.position.x + targetSize.width / 2;
  const targetY = targetNode.position.y + targetSize.height / 2;

  const centerX = (sourceX + targetX) / 2;
  const centerY = (sourceY + targetY) / 2;

  return { x: centerX, y: centerY };
});

const labelRect = computed(() => {
  if (!props.edge.properties.label) {
    return { x: 0, y: 0, width: 0, height: 0 };
  }

  const text = props.edge.properties.label;
  const fontSize = labelFontSize.value;
  
  // 简单的文本宽度估算
  const textWidth = text.length * fontSize * 0.6;
  const textHeight = fontSize + 8;
  
  return {
    x: -textWidth / 2 - 4,
    y: -textHeight / 2,
    width: textWidth + 8,
    height: textHeight,
  };
});

const labelTransform = computed(() => {
  const pos = labelPosition.value;
  return `translate(${pos.x}, ${pos.y})`;
});

const labelFontSize = computed(() => 12);

const labelBackgroundColor = computed(() => '#ffffff');

const labelBorderColor = computed(() => '#d9d9d9');

const labelTextColor = computed(() => '#262626');

// 事件处理
function handleClick(event: MouseEvent) {
  event.stopPropagation();
  emit('click', props.edge, event);
}

function handleDoubleClick(event: MouseEvent) {
  event.stopPropagation();
  emit('double-click', props.edge, event);
}

function handleContextMenu(event: MouseEvent) {
  event.preventDefault();
  event.stopPropagation();
  emit('context-menu', props.edge, event);
}
</script>

<style scoped>


@keyframes dash {
  to {
    stroke-dashoffset: -8;
  }
}

.workflow-edge {
  cursor: pointer;
}

.workflow-edge-path {
  transition: all 0.2s ease;
}

.workflow-edge:hover .workflow-edge-path {
  stroke-width: 3;
}

.workflow-edge-path-selected {
  stroke-width: 3;
}

.workflow-edge-selection {
  pointer-events: none;
}

.workflow-edge-hover-area {
  cursor: pointer;
}

.workflow-edge-animated-path {
  animation: dash 1s linear infinite;
}

.workflow-edge-label-bg {
  filter: drop-shadow(0 1px 2px rgb(0 0 0 / 10%));
}

.workflow-edge-label-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
  user-select: none;
}

/* 不同连线类型的特殊样式 */
.workflow-edge-bezier .workflow-edge-path {
  /* 贝塞尔曲线特殊样式 */
}

.workflow-edge-straight .workflow-edge-path {
  /* 直线特殊样式 */
}

.workflow-edge-step .workflow-edge-path {
  /* 阶梯线特殊样式 */
}

.workflow-edge-smoothstep .workflow-edge-path {
  /* 平滑阶梯线特殊样式 */
}
</style> 
