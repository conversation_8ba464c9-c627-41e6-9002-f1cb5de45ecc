<template>
  <div class="node-property-form">
    <Form
      :model="formData"
      layout="vertical"
      size="small"
      @finish="handleSubmit"
    >
      <!-- 基础属性 -->
      <Divider orientation="left">基础属性</Divider>
      
      <Form.Item label="节点名称" name="name" :rules="[{ required: true, message: '请输入节点名称' }]">
        <Input
          v-model:value="formData.name"
          placeholder="请输入节点名称"
          @change="handleChange"
        />
      </Form.Item>

      <Form.Item label="节点描述" name="description">
        <Textarea
          v-model:value="formData.description"
          placeholder="请输入节点描述"
          :rows="3"
          @change="handleChange"
        />
      </Form.Item>

      <!-- 审批节点特有属性 -->
      <template v-if="node.type === 'approval'">
        <Divider orientation="left">审批设置</Divider>

        <Form.Item label="审批人" name="approvers" :rules="[{ required: true, message: '请选择审批人' }]">
          <Select
            v-model:value="formData.approvers"
            mode="multiple"
            placeholder="请选择审批人"
            :options="approverOptions"
            :field-names="{ label: 'name', value: 'id' }"
            @change="handleChange"
          >
            <template #option="{ label, department, role }">
              <div class="approver-option">
                <div class="approver-name">{{ label }}</div>
                <div class="approver-info">{{ department }} · {{ role }}</div>
              </div>
            </template>
          </Select>
        </Form.Item>

        <Form.Item label="审批类型" name="approvalType">
          <RadioGroup
            v-model:value="formData.approvalType"
            @change="handleChange"
          >
            <Radio value="single">单人审批</Radio>
            <Radio value="all">全部审批</Radio>
            <Radio value="majority">多数审批</Radio>
          </RadioGroup>
        </Form.Item>

        <Form.Item label="审批时限（小时）" name="timeLimit">
          <InputNumber
            v-model:value="formData.timeLimit"
            :min="1"
            :max="720"
            placeholder="24"
            style="width: 100%"
            @change="handleChange"
          />
        </Form.Item>

        <Form.Item name="autoApprove">
          <Checkbox
            v-model:checked="formData.autoApprove"
            @change="handleChange"
          >
            超时自动审批
          </Checkbox>
        </Form.Item>

        <Collapse v-if="formData.autoApprove" size="small">
          <CollapsePanel key="escalation" header="升级设置">
            <Form.Item name="escalation">
              <Checkbox
                v-model:checked="formData.escalation"
                @change="handleChange"
              >
                启用升级
              </Checkbox>
            </Form.Item>

            <template v-if="formData.escalation">
              <Form.Item label="升级时间（小时）" name="escalationTime">
                <InputNumber
                  v-model:value="formData.escalationTime"
                  :min="1"
                  :max="720"
                  placeholder="48"
                  style="width: 100%"
                  @change="handleChange"
                />
              </Form.Item>

              <Form.Item label="升级审批人" name="escalationApprovers">
                <Select
                  v-model:value="formData.escalationApprovers"
                  mode="multiple"
                  placeholder="请选择升级审批人"
                  :options="approverOptions"
                  :field-names="{ label: 'name', value: 'id' }"
                  @change="handleChange"
                />
              </Form.Item>
            </template>
          </CollapsePanel>
        </Collapse>
      </template>

      <!-- 条件节点特有属性 -->
      <template v-if="node.type === 'condition'">
        <Divider orientation="left">条件设置</Divider>

        <Form.Item label="条件规则" name="conditions" :rules="[{ required: true, message: '请设置条件规则' }]">
          <div class="condition-rules">
            <div
              v-for="(condition, index) in formData.conditions"
              :key="condition.id"
              class="condition-rule"
            >
              <div class="condition-rule-header">
                <span>条件 {{ index + 1 }}</span>
                <Button
                  type="text"
                  size="small"
                  danger
                  @click="removeCondition(index)"
                >
                  <Trash2 :size="14" />
                </Button>
              </div>

              <Row :gutter="8">
                <Col :span="8">
                  <Input
                    v-model:value="condition.field"
                    placeholder="字段名"
                    size="small"
                    @change="handleChange"
                  />
                </Col>
                <Col :span="8">
                  <Select
                    v-model:value="condition.operator"
                    placeholder="操作符"
                    size="small"
                    :options="conditionOperators"
                    @change="handleChange"
                  />
                </Col>
                <Col :span="8">
                  <Input
                    v-model:value="condition.value"
                    placeholder="值"
                    size="small"
                    @change="handleChange"
                  />
                </Col>
              </Row>
            </div>

            <Button
              type="dashed"
              block
              size="small"
              @click="addCondition"
            >
              <Plus :size="14" />
              添加条件
            </Button>
          </div>
        </Form.Item>

        <Form.Item label="默认路径" name="defaultPath">
          <RadioGroup
            v-model:value="formData.defaultPath"
            @change="handleChange"
          >
            <Radio value="true">真（满足条件）</Radio>
            <Radio value="false">假（不满足条件）</Radio>
          </RadioGroup>
        </Form.Item>
      </template>

      <!-- 并行节点特有属性 -->
      <template v-if="node.type === 'parallel'">
        <Divider orientation="left">并行设置</Divider>

        <Form.Item label="分支设置" name="branches" :rules="[{ required: true, message: '至少需要2个分支' }]">
          <div class="branch-list">
            <div
              v-for="(branch, index) in formData.branches"
              :key="index"
              class="branch-item"
            >
              <Input
                v-model:value="formData.branches[index]"
                :placeholder="`分支 ${index + 1}`"
                size="small"
                @change="handleChange"
              />
              <Button
                v-if="formData.branches.length > 2"
                type="text"
                size="small"
                danger
                @click="removeBranch(index)"
              >
                <Trash2 :size="14" />
              </Button>
            </div>

            <Button
              type="dashed"
              block
              size="small"
              @click="addBranch"
            >
              <Plus :size="14" />
              添加分支
            </Button>
          </div>
        </Form.Item>
      </template>

      <!-- 样式设置 -->
      <Divider orientation="left">样式设置</Divider>

      <Form.Item label="背景颜色" name="backgroundColor">
        <div class="color-picker">
          <Input
            v-model:value="formData.backgroundColor"
            placeholder="#1890ff"
            @change="handleChange"
          />
          <div
            class="color-preview"
            :style="{ backgroundColor: formData.backgroundColor || nodeTemplate?.color }"
          />
        </div>
      </Form.Item>

      <Form.Item label="边框颜色" name="borderColor">
        <div class="color-picker">
          <Input
            v-model:value="formData.borderColor"
            placeholder="#1890ff"
            @change="handleChange"
          />
          <div
            class="color-preview"
            :style="{ backgroundColor: formData.borderColor || nodeTemplate?.color }"
          />
        </div>
      </Form.Item>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <Button type="primary" block @click="handleSubmit">
          保存更改
        </Button>
        <Button block style="margin-top: 8px" @click="handleReset">
          重置
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import {
  Form,
  Input,
  Textarea,
  Select,
  RadioGroup,
  Radio,
  InputNumber,
  Checkbox,
  Button,
  Divider,
  Collapse,
  CollapsePanel,
  Row,
  Col,
  message,
} from 'ant-design-vue';
import { Plus, Trash2 } from 'lucide-vue-next';
import type { WorkflowNode, Approver, ConditionRule } from '../../types';
import { DEFAULT_NODE_TEMPLATES, CONDITION_OPERATORS } from '../../config';
import { generateId } from '../../utils';

interface Props {
  node: WorkflowNode;
  approverDataSource?: Approver[];
}

interface Emits {
  (e: 'update', updates: Partial<WorkflowNode>): void;
}

const props = withDefaults(defineProps<Props>(), {
  approverDataSource: () => [],
});

const emit = defineEmits<Emits>();

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  // 审批节点属性
  approvers: [] as string[],
  approvalType: 'single' as 'single' | 'all' | 'majority',
  timeLimit: 24,
  autoApprove: false,
  escalation: false,
  escalationTime: 48,
  escalationApprovers: [] as string[],
  // 条件节点属性
  conditions: [] as ConditionRule[],
  defaultPath: 'true' as 'true' | 'false',
  // 并行节点属性
  branches: [] as string[],
  // 样式属性
  backgroundColor: '',
  borderColor: '',
});

// 获取节点模板
const nodeTemplate = computed(() => {
  return DEFAULT_NODE_TEMPLATES.find(template => template.type === props.node.type);
});

// 审批人选项
const approverOptions = computed(() => {
  return props.approverDataSource.map(approver => ({
    ...approver,
    label: approver.name,
    value: approver.id,
  }));
});

// 条件操作符选项
const conditionOperators = computed(() => {
  return CONDITION_OPERATORS.map(op => ({
    label: `${op.label} (${op.symbol})`,
    value: op.value,
  }));
});

// 初始化表单数据
function initFormData() {
  const { properties } = props.node;
  
  Object.assign(formData, {
    name: properties.name || '',
    description: properties.description || '',
    // 审批节点属性
    approvers: properties.approvers?.map(a => a.id) || [],
    approvalType: properties.approvalType || 'single',
    timeLimit: properties.timeLimit || 24,
    autoApprove: properties.autoApprove || false,
    escalation: properties.escalation || false,
    escalationTime: properties.escalationTime || 48,
    escalationApprovers: properties.escalationApprovers?.map(a => a.id) || [],
    // 条件节点属性
    conditions: properties.conditions || [],
    defaultPath: properties.defaultPath || 'true',
    // 并行节点属性
    branches: properties.branches || ['分支1', '分支2'],
    // 样式属性
    backgroundColor: properties.backgroundColor || '',
    borderColor: properties.borderColor || '',
  });
}

// 监听节点变化
watch(
  () => props.node,
  () => {
    initFormData();
  },
  { immediate: true }
);

// 添加条件
function addCondition() {
  formData.conditions.push({
    id: generateId(),
    field: '',
    operator: 'eq',
    value: '',
  });
  handleChange();
}

// 删除条件
function removeCondition(index: number) {
  formData.conditions.splice(index, 1);
  handleChange();
}

// 添加分支
function addBranch() {
  formData.branches.push(`分支${formData.branches.length + 1}`);
  handleChange();
}

// 删除分支
function removeBranch(index: number) {
  if (formData.branches.length > 2) {
    formData.branches.splice(index, 1);
    handleChange();
  }
}

// 处理变更
function handleChange() {
  // 实时更新
  const updates = buildUpdates();
  emit('update', updates);
}

// 构建更新数据
function buildUpdates(): Partial<WorkflowNode> {
  const properties: any = {
    name: formData.name,
    description: formData.description,
    backgroundColor: formData.backgroundColor,
    borderColor: formData.borderColor,
  };

  // 审批节点属性
  if (props.node.type === 'approval') {
    properties.approvers = formData.approvers.map(id => 
      props.approverDataSource.find(a => a.id === id)
    ).filter(Boolean);
    properties.approvalType = formData.approvalType;
    properties.timeLimit = formData.timeLimit;
    properties.autoApprove = formData.autoApprove;
    properties.escalation = formData.escalation;
    properties.escalationTime = formData.escalationTime;
    properties.escalationApprovers = formData.escalationApprovers.map(id => 
      props.approverDataSource.find(a => a.id === id)
    ).filter(Boolean);
  }

  // 条件节点属性
  if (props.node.type === 'condition') {
    properties.conditions = formData.conditions;
    properties.defaultPath = formData.defaultPath;
  }

  // 并行节点属性
  if (props.node.type === 'parallel') {
    properties.branches = formData.branches;
  }

  return { properties };
}

// 提交表单
function handleSubmit() {
  const updates = buildUpdates();
  emit('update', updates);
  message.success('属性已保存');
}

// 重置表单
function handleReset() {
  initFormData();
  message.info('属性已重置');
}
</script>

<style scoped>
.node-property-form {
  /* Form 组件样式由 Ant Design Vue 管理 */
}

/* 审批人选项 */
.approver-option {
  display: flex;
  flex-direction: column;
}

.approver-name {
  font-weight: 500;
  color: #262626;
}

.approver-info {
  font-size: 12px;
  color: #8c8c8c;
}

/* 条件规则 */
.condition-rules {
  /* ... */
}

.condition-rule {
  padding: 12px;
  margin-bottom: 12px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.condition-rule-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #595959;
}

/* 分支列表 */
.branch-list {
  /* ... */
}

.branch-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.branch-item .ant-input {
  flex: 1;
  margin-right: 8px;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  align-items: center;
}

.color-picker .ant-input {
  flex: 1;
  margin-right: 8px;
}

.color-preview {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

/* 操作按钮 */
.form-actions {
  padding-top: 16px;
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
}
</style> 
