import type { NodeConfig, NodeType } from '../types/workflow';

/**
 * 审批工作流节点配置
 */
export const WORKFLOW_NODE_CONFIGS: Record<NodeType, NodeConfig> = {
  start: {
    type: 'start',
    name: '开始',
    icon: 'play-circle',
    color: '#52c41a',
    description: '工作流开始节点',
    defaultProperties: {
      name: '开始',
      description: '工作流的起始节点',
      triggerType: 'manual', // manual, auto, schedule
      triggerCondition: '',
      priority: 'normal', // low, normal, high, urgent
      tags: [],
    },
    allowedConnections: {
      target: [], // 开始节点不能作为连线终点
    },
  },
  
  approval: {
    type: 'approval',
    name: '审批',
    icon: 'user-check',
    color: '#1890ff',
    description: '审批节点，需要指定审批人',
    defaultProperties: {
      name: '审批节点',
      description: '需要审批人处理的节点',
      approvers: [],
      approvalType: 'single', // single, all, majority
      timeLimit: 24,
      autoApprove: false,
      escalation: false, // 是否启用升级
      escalationTime: 48, // 升级时间（小时）
      escalationApprovers: [], // 升级审批人
      notificationEnabled: true, // 是否启用通知
      notificationTemplate: 'default', // 通知模板
      allowDelegate: true, // 是否允许委托
      allowReject: true, // 是否允许拒绝
      rejectToStart: false, // 拒绝后是否回到开始
      priority: 'normal', // low, normal, high, urgent
      tags: [],
    },
  },
  
  condition: {
    type: 'condition',
    name: '条件',
    icon: 'git-branch',
    color: '#fa8c16',
    description: '条件判断节点，根据条件分支',
    defaultProperties: {
      name: '条件判断',
      description: '根据条件进行分支判断',
      conditions: [],
      logicOperator: 'AND', // AND, OR
      defaultBranch: 'else', // 默认分支
      enableElseBranch: true, // 是否启用else分支
      priority: 'normal',
      tags: [],
    },
  },
  
  parallel: {
    type: 'parallel',
    name: '并行',
    icon: 'fork',
    color: '#722ed1',
    description: '并行处理节点，同时执行多个分支',
    defaultProperties: {
      name: '并行处理',
      branches: ['分支1', '分支2'],
    },
  },
  
  merge: {
    type: 'merge',
    name: '合并',
    icon: 'merge',
    color: '#eb2f96',
    description: '合并节点，等待所有分支完成',
    defaultProperties: {
      name: '合并节点',
    },
  },
  
  end: {
    type: 'end',
    name: '结束',
    icon: 'stop',
    color: '#f5222d',
    description: '工作流结束节点',
    defaultProperties: {
      name: '结束',
      description: '工作流的结束节点',
      endType: 'normal', // normal, success, failure, cancel
      resultMessage: '', // 结束时的结果消息
      notifyUsers: [], // 需要通知的用户
      executeScript: '', // 结束时执行的脚本
      priority: 'normal',
      tags: [],
    },
    allowedConnections: {
      source: [], // 结束节点不能作为连线起点
    },
  },
};

/**
 * 获取节点配置
 */
export function getNodeConfig(type: NodeType): NodeConfig {
  return WORKFLOW_NODE_CONFIGS[type];
}

/**
 * 获取所有节点类型
 */
export function getAllNodeTypes(): NodeType[] {
  return Object.keys(WORKFLOW_NODE_CONFIGS) as NodeType[];
}

/**
 * 获取节点面板配置
 */
export function getNodePanelConfig() {
  return [
    {
      category: '基础节点',
      nodes: [
        WORKFLOW_NODE_CONFIGS.start,
        WORKFLOW_NODE_CONFIGS.end,
      ],
    },
    {
      category: '流程节点',
      nodes: [
        WORKFLOW_NODE_CONFIGS.approval,
        WORKFLOW_NODE_CONFIGS.condition,
      ],
    },
    {
      category: '控制节点',
      nodes: [
        WORKFLOW_NODE_CONFIGS.parallel,
        WORKFLOW_NODE_CONFIGS.merge,
      ],
    },
  ];
}

/**
 * 审批类型选项
 */
export const APPROVAL_TYPE_OPTIONS = [
  { value: 'single', label: '单人审批', description: '任意一人审批即可' },
  { value: 'all', label: '全部审批', description: '所有人都需要审批' },
  { value: 'majority', label: '多数审批', description: '超过半数人审批即可' },
];

/**
 * 条件操作符选项
 */
export const CONDITION_OPERATORS = [
  { value: 'eq', label: '等于', symbol: '=' },
  { value: 'ne', label: '不等于', symbol: '≠' },
  { value: 'gt', label: '大于', symbol: '>' },
  { value: 'gte', label: '大于等于', symbol: '≥' },
  { value: 'lt', label: '小于', symbol: '<' },
  { value: 'lte', label: '小于等于', symbol: '≤' },
  { value: 'in', label: '包含', symbol: '∈' },
  { value: 'not_in', label: '不包含', symbol: '∉' },
  { value: 'contains', label: '包含文本', symbol: '⊃' },
  { value: 'starts_with', label: '开头是', symbol: '⊃' },
  { value: 'ends_with', label: '结尾是', symbol: '⊃' },
];

/**
 * 默认工作流数据
 */
export const DEFAULT_WORKFLOW_DATA = {
  nodes: [
    {
      id: 'start-1',
      type: 'start' as NodeType,
      x: 200,
      y: 200,
      properties: {
        name: '开始',
      },
      text: {
        x: 200,
        y: 240,
        value: '开始',
      },
    },
    {
      id: 'end-1',
      type: 'end' as NodeType,
      x: 600,
      y: 200,
      properties: {
        name: '结束',
      },
      text: {
        x: 600,
        y: 240,
        value: '结束',
      },
    },
  ],
  edges: [],
};

/**
 * 工作流模板
 */
export const WORKFLOW_TEMPLATES = [
  {
    id: 'simple-approval',
    name: '简单审批流程',
    description: '包含开始、审批、结束的基础流程',
    category: '基础模板',
    data: {
      nodes: [
        {
          id: 'start-1',
          type: 'start' as NodeType,
          x: 150,
          y: 200,
          properties: { name: '开始' },
          text: { x: 150, y: 240, value: '开始' },
        },
        {
          id: 'approval-1',
          type: 'approval' as NodeType,
          x: 350,
          y: 200,
          properties: {
            name: '部门审批',
            approvers: [],
            approvalType: 'single',
            timeLimit: 24,
          },
          text: { x: 350, y: 240, value: '部门审批' },
        },
        {
          id: 'end-1',
          type: 'end' as NodeType,
          x: 550,
          y: 200,
          properties: { name: '结束' },
          text: { x: 550, y: 240, value: '结束' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          type: 'polyline',
          sourceNodeId: 'start-1',
          targetNodeId: 'approval-1',
          startPoint: { x: 170, y: 200 },
          endPoint: { x: 330, y: 200 },
          properties: {},
        },
        {
          id: 'edge-2',
          type: 'polyline',
          sourceNodeId: 'approval-1',
          targetNodeId: 'end-1',
          startPoint: { x: 370, y: 200 },
          endPoint: { x: 530, y: 200 },
          properties: {},
        },
      ],
    },
  },
  {
    id: 'conditional-approval',
    name: '条件审批流程',
    description: '根据条件进行不同的审批路径',
    category: '条件模板',
    data: {
      nodes: [
        {
          id: 'start-1',
          type: 'start' as NodeType,
          x: 100,
          y: 200,
          properties: { name: '开始' },
          text: { x: 100, y: 240, value: '开始' },
        },
        {
          id: 'condition-1',
          type: 'condition' as NodeType,
          x: 300,
          y: 200,
          properties: {
            name: '金额判断',
            conditions: [
              { field: 'amount', operator: 'gt', value: 10000, label: '金额 > 10000' },
            ],
          },
          text: { x: 300, y: 240, value: '金额判断' },
        },
        {
          id: 'approval-1',
          type: 'approval' as NodeType,
          x: 500,
          y: 120,
          properties: {
            name: '高级审批',
            approvers: [],
            approvalType: 'all',
          },
          text: { x: 500, y: 160, value: '高级审批' },
        },
        {
          id: 'approval-2',
          type: 'approval' as NodeType,
          x: 500,
          y: 280,
          properties: {
            name: '普通审批',
            approvers: [],
            approvalType: 'single',
          },
          text: { x: 500, y: 320, value: '普通审批' },
        },
        {
          id: 'end-1',
          type: 'end' as NodeType,
          x: 700,
          y: 200,
          properties: { name: '结束' },
          text: { x: 700, y: 240, value: '结束' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          type: 'polyline',
          sourceNodeId: 'start-1',
          targetNodeId: 'condition-1',
          startPoint: { x: 120, y: 200 },
          endPoint: { x: 280, y: 200 },
          properties: {},
        },
        {
          id: 'edge-2',
          type: 'polyline',
          sourceNodeId: 'condition-1',
          targetNodeId: 'approval-1',
          startPoint: { x: 320, y: 180 },
          endPoint: { x: 480, y: 140 },
          properties: { label: '是' },
          text: { x: 400, y: 150, value: '是' },
        },
        {
          id: 'edge-3',
          type: 'polyline',
          sourceNodeId: 'condition-1',
          targetNodeId: 'approval-2',
          startPoint: { x: 320, y: 220 },
          endPoint: { x: 480, y: 260 },
          properties: { label: '否' },
          text: { x: 400, y: 250, value: '否' },
        },
        {
          id: 'edge-4',
          type: 'polyline',
          sourceNodeId: 'approval-1',
          targetNodeId: 'end-1',
          startPoint: { x: 520, y: 140 },
          endPoint: { x: 680, y: 180 },
          properties: {},
        },
        {
          id: 'edge-5',
          type: 'polyline',
          sourceNodeId: 'approval-2',
          targetNodeId: 'end-1',
          startPoint: { x: 520, y: 260 },
          endPoint: { x: 680, y: 220 },
          properties: {},
        },
      ],
    },
  },
];
