<template>
  <div class="workflow-property-panel">
    <div class="workflow-property-panel-header">
      <h3 class="panel-title">属性设置</h3>
      <Button
        v-if="selectedElement"
        type="text"
        size="small"
        @click="clearSelection"
      >
        <X :size="16" />
      </Button>
    </div>

    <div class="workflow-property-panel-content">
      <!-- 未选择任何元素 -->
      <div v-if="!selectedElement" class="empty-state">
        <div class="empty-icon">
          <Settings :size="48" color="#d9d9d9" />
        </div>
        <div class="empty-text">
          <p>请选择节点或连线</p>
          <p class="empty-description">选择工作流中的节点或连线来编辑其属性</p>
        </div>
      </div>

      <!-- 节点属性编辑 -->
      <div v-else-if="selectedNode" class="node-properties">
        <NodePropertyForm
          :key="selectedNode.id"
          :node="selectedNode"
          :approver-data-source="approverDataSource"
          @update="handleNodeUpdate"
        />
      </div>

      <!-- 连线属性编辑 -->
      <div v-else-if="selectedEdge" class="edge-properties">
        <EdgePropertyForm
          :key="selectedEdge.id"
          :edge="selectedEdge"
          @update="handleEdgeUpdate"
        />
      </div>

      <!-- 多选状态 -->
      <div v-else class="multi-selection">
        <div class="multi-selection-info">
          <div class="multi-selection-icon">
            <Layers :size="24" color="#1890ff" />
          </div>
          <div class="multi-selection-text">
            <p>已选择 {{ selectionCount }} 个元素</p>
            <p class="multi-selection-description">
              包含 {{ selectedNodes.length }} 个节点和 {{ selectedEdges.length }} 条连线
            </p>
          </div>
        </div>

        <div class="multi-selection-actions">
          <Button
            type="primary"
            block
            @click="alignSelectedElements"
          >
            <AlignLeft :size="16" />
            对齐元素
          </Button>
          <Button
            block
            style="margin-top: 8px"
            @click="deleteSelectedElements"
          >
            <Trash2 :size="16" />
            删除选中
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Button } from 'ant-design-vue';
import {
  Settings,
  X,
  Layers,
  AlignLeft,
  Trash2,
} from 'lucide-vue-next';
import type {
  WorkflowNode,
  WorkflowEdge,
  Approver,
} from '../../types';
import NodePropertyForm from './NodePropertyForm.vue';
import EdgePropertyForm from './EdgePropertyForm.vue';

interface Props {
  selectedNodes?: WorkflowNode[];
  selectedEdges?: WorkflowEdge[];
  approverDataSource?: Approver[];
}

interface Emits {
  (e: 'node-update', nodeId: string, updates: Partial<WorkflowNode>): void;
  (e: 'edge-update', edgeId: string, updates: Partial<WorkflowEdge>): void;
  (e: 'clear-selection'): void;
  (e: 'align-elements'): void;
  (e: 'delete-elements'): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedNodes: () => [],
  selectedEdges: () => [],
  approverDataSource: () => [],
});

const emit = defineEmits<Emits>();

// 计算属性
const selectedElement = computed(() => {
  const totalSelected = props.selectedNodes.length + props.selectedEdges.length;
  return totalSelected > 0;
});

const selectedNode = computed(() => {
  return props.selectedNodes.length === 1 ? props.selectedNodes[0] : null;
});

const selectedEdge = computed(() => {
  return props.selectedEdges.length === 1 ? props.selectedEdges[0] : null;
});

const selectionCount = computed(() => {
  return props.selectedNodes.length + props.selectedEdges.length;
});

// 事件处理
function handleNodeUpdate(updates: Partial<WorkflowNode>) {
  if (selectedNode.value) {
    emit('node-update', selectedNode.value.id, updates);
  }
}

function handleEdgeUpdate(updates: Partial<WorkflowEdge>) {
  if (selectedEdge.value) {
    emit('edge-update', selectedEdge.value.id, updates);
  }
}

function clearSelection() {
  emit('clear-selection');
}

function alignSelectedElements() {
  emit('align-elements');
}

function deleteSelectedElements() {
  emit('delete-elements');
}
</script>

<style scoped>
.workflow-property-panel {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  width: 240px;
  height: 100%;
  background: #fff;
  border-left: 1px solid #f0f0f0;
}

.workflow-property-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.panel-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.workflow-property-panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text p {
  margin: 0;
  font-size: 14px;
  color: #8c8c8c;
}

.empty-description {
  margin-top: 8px !important;
  font-size: 12px !important;
}

/* 节点和连线属性 */
.node-properties,
.edge-properties {
  /* 样式在子组件中定义 */
}

/* 多选状态 */
.multi-selection {
  /* ... */
}

.multi-selection-info {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 24px;
  background: #f6f8ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
}

.multi-selection-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

.multi-selection-text p {
  margin: 0;
  font-size: 14px;
  color: #262626;
}

.multi-selection-description {
  margin-top: 4px !important;
  font-size: 12px !important;
  color: #8c8c8c !important;
}

.multi-selection-actions {
  /* Button 组件的样式由 Ant Design Vue 管理 */
}

/* 滚动条样式 */
.workflow-property-panel-content::-webkit-scrollbar {
  width: 6px;
}

.workflow-property-panel-content::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.workflow-property-panel-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.workflow-property-panel-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style> 
