import { v4 as uuidv4 } from 'uuid';
import type {
  WorkflowNode,
  WorkflowEdge,
  WorkflowData,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  NodePosition,
  NodeType,
  EdgeType,
} from '../types';

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return uuidv4();
}

/**
 * 计算两点之间的距离
 */
export function calculateDistance(p1: NodePosition, p2: NodePosition): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 计算贝塞尔曲线路径
 */
export function getBezierPath(
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  sourcePosition = 'right',
  targetPosition = 'left'
): string {
  const curvature = 0.25;
  const dx = targetX - sourceX;
  const dy = targetY - sourceY;

  let sourceControlX = sourceX;
  let sourceControlY = sourceY;
  let targetControlX = targetX;
  let targetControlY = targetY;

  switch (sourcePosition) {
    case 'right':
      sourceControlX = sourceX + dx * curvature;
      break;
    case 'left':
      sourceControlX = sourceX - dx * curvature;
      break;
    case 'top':
      sourceControlY = sourceY - dy * curvature;
      break;
    case 'bottom':
      sourceControlY = sourceY + dy * curvature;
      break;
  }

  switch (targetPosition) {
    case 'right':
      targetControlX = targetX + dx * curvature;
      break;
    case 'left':
      targetControlX = targetX - dx * curvature;
      break;
    case 'top':
      targetControlY = targetY - dy * curvature;
      break;
    case 'bottom':
      targetControlY = targetY + dy * curvature;
      break;
  }

  return `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`;
}

/**
 * 计算直线路径
 */
export function getStraightPath(
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number
): string {
  return `M${sourceX},${sourceY} L${targetX},${targetY}`;
}

/**
 * 计算阶梯线路径
 */
export function getStepPath(
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  sourcePosition = 'right'
): string {
  const centerX = sourceX + (targetX - sourceX) / 2;

  if (sourcePosition === 'right' || sourcePosition === 'left') {
    return `M${sourceX},${sourceY} L${centerX},${sourceY} L${centerX},${targetY} L${targetX},${targetY}`;
  } else {
    const centerY = sourceY + (targetY - sourceY) / 2;
    return `M${sourceX},${sourceY} L${sourceX},${centerY} L${targetX},${centerY} L${targetX},${targetY}`;
  }
}

/**
 * 计算平滑阶梯线路径
 */
export function getSmoothStepPath(
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  sourcePosition = 'right',
  radius = 5
): string {
  const centerX = sourceX + (targetX - sourceX) / 2;
  const centerY = sourceY + (targetY - sourceY) / 2;

  if (sourcePosition === 'right' || sourcePosition === 'left') {
    const step1X = centerX - radius;
    const step2X = centerX + radius;

    return `
      M${sourceX},${sourceY}
      L${step1X},${sourceY}
      Q${centerX},${sourceY} ${centerX},${sourceY + (sourceY < targetY ? radius : -radius)}
      L${centerX},${targetY - (sourceY < targetY ? radius : -radius)}
      Q${centerX},${targetY} ${step2X},${targetY}
      L${targetX},${targetY}
    `.trim();
  } else {
    const step1Y = centerY - radius;
    const step2Y = centerY + radius;

    return `
      M${sourceX},${sourceY}
      L${sourceX},${step1Y}
      Q${sourceX},${centerY} ${sourceX + (sourceX < targetX ? radius : -radius)},${centerY}
      L${targetX - (sourceX < targetX ? radius : -radius)},${centerY}
      Q${targetX},${centerY} ${targetX},${step2Y}
      L${targetX},${targetY}
    `.trim();
  }
}

/**
 * 根据连线类型计算路径
 */
export function getEdgePath(
  type: EdgeType,
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  sourcePosition = 'right',
  targetPosition = 'left'
): string {
  switch (type) {
    case 'straight':
      return getStraightPath(sourceX, sourceY, targetX, targetY);
    case 'step':
      return getStepPath(sourceX, sourceY, targetX, targetY, sourcePosition);
    case 'smoothstep':
      return getSmoothStepPath(sourceX, sourceY, targetX, targetY, sourcePosition);
    case 'bezier':
    default:
      return getBezierPath(sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition);
  }
}

/**
 * 获取节点的连接点位置
 */
export function getHandlePosition(
  node: WorkflowNode,
  handleId: string
): { x: number; y: number } | null {
  const handle = node.handles?.find(h => h.id === handleId);
  if (!handle) return null;

  const { position: nodePos, size = { width: 120, height: 60 } } = node;
  const { position: handlePos } = handle;

  let x = nodePos.x;
  let y = nodePos.y;

  switch (handlePos) {
    case 'top':
      x += size.width / 2;
      break;
    case 'right':
      x += size.width;
      y += size.height / 2;
      break;
    case 'bottom':
      x += size.width / 2;
      y += size.height;
      break;
    case 'left':
      y += size.height / 2;
      break;
  }

  return { x, y };
}

/**
 * 检查两个矩形是否相交
 */
export function isRectIntersecting(
  rect1: { x: number; y: number; width: number; height: number },
  rect2: { x: number; y: number; width: number; height: number }
): boolean {
  return (
    rect1.x < rect2.x + rect2.width &&
    rect1.x + rect1.width > rect2.x &&
    rect1.y < rect2.y + rect2.height &&
    rect1.y + rect1.height > rect2.y
  );
}

/**
 * 检查点是否在矩形内
 */
export function isPointInRect(
  point: { x: number; y: number },
  rect: { x: number; y: number; width: number; height: number }
): boolean {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  );
}

/**
 * 对齐到网格
 */
export function snapToGrid(position: NodePosition, gridSize: number): NodePosition {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  };
}

/**
 * 计算节点边界框
 */
export function getNodeBounds(nodes: WorkflowNode[]): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  if (nodes.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  nodes.forEach(node => {
    const { position, size = { width: 120, height: 60 } } = node;
    minX = Math.min(minX, position.x);
    minY = Math.min(minY, position.y);
    maxX = Math.max(maxX, position.x + size.width);
    maxY = Math.max(maxY, position.y + size.height);
  });

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  };
}

/**
 * 获取连接到节点的边
 */
export function getConnectedEdges(nodeId: string, edges: WorkflowEdge[]): WorkflowEdge[] {
  return edges.filter(edge => edge.sourceNodeId === nodeId || edge.targetNodeId === nodeId);
}

/**
 * 获取节点的输入边
 */
export function getIncomingEdges(nodeId: string, edges: WorkflowEdge[]): WorkflowEdge[] {
  return edges.filter(edge => edge.targetNodeId === nodeId);
}

/**
 * 获取节点的输出边
 */
export function getOutgoingEdges(nodeId: string, edges: WorkflowEdge[]): WorkflowEdge[] {
  return edges.filter(edge => edge.sourceNodeId === nodeId);
}

/**
 * 检查是否存在循环依赖
 */
export function hasCircularDependency(nodes: WorkflowNode[], edges: WorkflowEdge[]): boolean {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();

  function dfs(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) {
      return true; // 发现循环
    }

    if (visited.has(nodeId)) {
      return false;
    }

    visited.add(nodeId);
    recursionStack.add(nodeId);

    const outgoingEdges = getOutgoingEdges(nodeId, edges);
    for (const edge of outgoingEdges) {
      if (dfs(edge.targetNodeId)) {
        return true;
      }
    }

    recursionStack.delete(nodeId);
    return false;
  }

  for (const node of nodes) {
    if (!visited.has(node.id) && dfs(node.id)) {
      return true;
    }
  }

  return false;
}

/**
 * 查找起始节点
 */
export function findStartNodes(nodes: WorkflowNode[]): WorkflowNode[] {
  return nodes.filter(node => node.type === 'start');
}

/**
 * 查找结束节点
 */
export function findEndNodes(nodes: WorkflowNode[]): WorkflowNode[] {
  return nodes.filter(node => node.type === 'end');
}

/**
 * 检查节点是否可达
 */
export function isNodeReachable(
  targetNodeId: string,
  startNodeIds: string[],
  edges: WorkflowEdge[]
): boolean {
  const visited = new Set<string>();
  const queue = [...startNodeIds];

  while (queue.length > 0) {
    const currentNodeId = queue.shift()!;

    if (currentNodeId === targetNodeId) {
      return true;
    }

    if (visited.has(currentNodeId)) {
      continue;
    }

    visited.add(currentNodeId);

    const outgoingEdges = getOutgoingEdges(currentNodeId, edges);
    for (const edge of outgoingEdges) {
      if (!visited.has(edge.targetNodeId)) {
        queue.push(edge.targetNodeId);
      }
    }
  }

  return false;
}

/**
 * 验证工作流
 */
export function validateWorkflow(data: WorkflowData): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  const { nodes, edges } = data;

  // 基本检查
  if (nodes.length === 0) {
    errors.push({
      id: generateId(),
      type: 'error',
      message: '工作流至少需要包含一个节点',
    });
    return { valid: false, errors, warnings };
  }

  // 检查开始节点
  const startNodes = findStartNodes(nodes);
  if (startNodes.length === 0) {
    errors.push({
      id: generateId(),
      type: 'error',
      message: '工作流必须包含一个开始节点',
    });
  } else if (startNodes.length > 1) {
    errors.push({
      id: generateId(),
      type: 'error',
      message: '工作流只能包含一个开始节点',
    });
  }

  // 检查结束节点
  const endNodes = findEndNodes(nodes);
  if (endNodes.length === 0) {
    warnings.push({
      id: generateId(),
      type: 'warning',
      message: '建议添加结束节点',
    });
  }

  // 检查节点属性
  for (const node of nodes) {
    // 检查节点名称
    if (!node.properties.name) {
      warnings.push({
        id: generateId(),
        type: 'warning',
        message: '建议为节点设置名称',
        nodeId: node.id,
      });
    }

    // 检查审批节点
    if (node.type === 'approval') {
      if (!node.properties.approvers || node.properties.approvers.length === 0) {
        errors.push({
          id: generateId(),
          type: 'error',
          message: '审批节点必须设置审批人',
          nodeId: node.id,
        });
      }

      if (node.properties.timeLimit && node.properties.timeLimit <= 0) {
        errors.push({
          id: generateId(),
          type: 'error',
          message: '审批时限必须大于0',
          nodeId: node.id,
        });
      }

      // 检查审批人信息
      if (node.properties.approvers) {
        node.properties.approvers.forEach((approver, index) => {
          if (!approver.id || !approver.name) {
            errors.push({
              id: generateId(),
              type: 'error',
              message: `审批人${index + 1}信息不完整`,
              nodeId: node.id,
            });
          }
        });
      }
    }

    // 检查条件节点
    if (node.type === 'condition') {
      if (!node.properties.conditions || node.properties.conditions.length === 0) {
        errors.push({
          id: generateId(),
          type: 'error',
          message: '条件节点必须设置判断条件',
          nodeId: node.id,
        });
      } else {
        node.properties.conditions.forEach((condition, index) => {
          if (!condition.field || !condition.operator || condition.value === undefined) {
            errors.push({
              id: generateId(),
              type: 'error',
              message: `条件${index + 1}配置不完整`,
              nodeId: node.id,
            });
          }
        });
      }
    }

    // 检查并行节点
    if (node.type === 'parallel') {
      if (!node.properties.branches || node.properties.branches.length < 2) {
        errors.push({
          id: generateId(),
          type: 'error',
          message: '并行节点至少需要2个分支',
          nodeId: node.id,
        });
      }
    }
  }

  // 检查连线
  for (const edge of edges) {
    const sourceNode = nodes.find(n => n.id === edge.sourceNodeId);
    const targetNode = nodes.find(n => n.id === edge.targetNodeId);

    if (!sourceNode || !targetNode) {
      errors.push({
        id: generateId(),
        type: 'error',
        message: '连线配置错误：找不到对应的节点',
        edgeId: edge.id,
      });
      continue;
    }

    // 检查开始节点不能作为连线终点
    if (targetNode.type === 'start') {
      errors.push({
        id: generateId(),
        type: 'error',
        message: '开始节点不能作为连线的终点',
        edgeId: edge.id,
      });
    }

    // 检查结束节点不能作为连线起点
    if (sourceNode.type === 'end') {
      errors.push({
        id: generateId(),
        type: 'error',
        message: '结束节点不能作为连线的起点',
        edgeId: edge.id,
      });
    }

    // 检查自连接
    if (edge.sourceNodeId === edge.targetNodeId) {
      errors.push({
        id: generateId(),
        type: 'error',
        message: '节点不能连接到自身',
        edgeId: edge.id,
      });
    }
  }

  // 检查孤立节点
  for (const node of nodes) {
    const connectedEdges = getConnectedEdges(node.id, edges);
    if (connectedEdges.length === 0 && nodes.length > 1) {
      warnings.push({
        id: generateId(),
        type: 'warning',
        message: '发现孤立节点',
        nodeId: node.id,
      });
    }

    // 检查输入输出连线（除了开始和结束节点）
    if (node.type !== 'start' && node.type !== 'end') {
      const incomingEdges = getIncomingEdges(node.id, edges);
      const outgoingEdges = getOutgoingEdges(node.id, edges);

      if (incomingEdges.length === 0) {
        warnings.push({
          id: generateId(),
          type: 'warning',
          message: '节点没有输入连线',
          nodeId: node.id,
        });
      }

      if (outgoingEdges.length === 0) {
        warnings.push({
          id: generateId(),
          type: 'warning',
          message: '节点没有输出连线',
          nodeId: node.id,
        });
      }
    }
  }

  // 检查循环依赖
  if (hasCircularDependency(nodes, edges)) {
    errors.push({
      id: generateId(),
      type: 'error',
      message: '工作流中存在循环依赖',
    });
  }

  // 检查可达性
  if (startNodes.length > 0) {
    const startNodeIds = startNodes.map(n => n.id);
    for (const node of nodes) {
      if (node.type !== 'start' && !isNodeReachable(node.id, startNodeIds, edges)) {
        warnings.push({
          id: generateId(),
          type: 'warning',
          message: '节点从开始节点不可达',
          nodeId: node.id,
        });
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 克隆工作流数据
 */
export function cloneWorkflowData(data: WorkflowData): WorkflowData {
  return JSON.parse(JSON.stringify(data));
}

/**
 * 合并工作流数据
 */
export function mergeWorkflowData(target: WorkflowData, source: Partial<WorkflowData>): WorkflowData {
  return {
    ...target,
    ...source,
    nodes: source.nodes || target.nodes,
    edges: source.edges || target.edges,
    viewport: { ...target.viewport, ...source.viewport },
    metadata: { ...target.metadata, ...source.metadata },
  };
}

/**
 * 导出工作流数据为JSON字符串
 */
export function exportWorkflowData(data: WorkflowData, pretty = false): string {
  return JSON.stringify(data, null, pretty ? 2 : 0);
}

/**
 * 从JSON字符串导入工作流数据
 */
export function importWorkflowData(jsonString: string): WorkflowData | null {
  try {
    const data = JSON.parse(jsonString);
    // 验证数据结构
    if (!data.nodes || !Array.isArray(data.nodes)) {
      throw new Error('Invalid workflow data: missing nodes array');
    }
    if (!data.edges || !Array.isArray(data.edges)) {
      throw new Error('Invalid workflow data: missing edges array');
    }
    return data;
  } catch (error) {
    console.error('Failed to import workflow data:', error);
    return null;
  }
}

export default {
  generateId,
  calculateDistance,
  getBezierPath,
  getStraightPath,
  getStepPath,
  getSmoothStepPath,
  getEdgePath,
  getHandlePosition,
  isRectIntersecting,
  isPointInRect,
  snapToGrid,
  getNodeBounds,
  getConnectedEdges,
  getIncomingEdges,
  getOutgoingEdges,
  hasCircularDependency,
  findStartNodes,
  findEndNodes,
  isNodeReachable,
  validateWorkflow,
  cloneWorkflowData,
  mergeWorkflowData,
  exportWorkflowData,
  importWorkflowData,
}; 
