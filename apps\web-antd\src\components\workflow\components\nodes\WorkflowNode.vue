<script setup lang="ts">
import type { Handle, NodeTemplate, WorkflowNode } from '../../types';

import { computed, inject } from 'vue';

import {
  Circle,
  GitBranch,
  PlayCircle,
  Share,
  StopCircle,
  TableCellsMerge,
  UserCheck,
} from 'lucide-vue-next';

import { DEFAULT_NODE_TEMPLATES } from '../../config';

interface Props {
  node: WorkflowNode;
  selected?: boolean;
  scale?: number;
  showDebugInfo?: boolean;
}

interface Emits {
  (e: 'click', node: WorkflowNode, event: MouseEvent): void;
  (e: 'double-click', node: WorkflowNode, event: MouseEvent): void;
  (e: 'context-menu', node: WorkflowNode, event: MouseEvent): void;
  (e: 'mouse-down', node: WorkflowNode, event: MouseEvent): void;
  (e: 'mouse-up', node: WorkflowNode, event: MouseEvent): void;
  (e: 'handle-mouse-down', handle: Handle, event: MouseEvent): void;
  (e: 'handle-mouse-up', handle: Handle, event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  scale: 1,
  showDebugInfo: false,
});

const emit = defineEmits<Emits>();

// 注入工作流配置
const workflowConfig = inject('workflowConfig', {});

// 获取节点模板
const nodeTemplate = computed<NodeTemplate | undefined>(() => {
  return DEFAULT_NODE_TEMPLATES.find(
    (template) => template.type === props.node.type,
  );
});

// 节点样式类
const nodeClasses = computed(() => [
  'workflow-node',
  `workflow-node-${props.node.type}`,
  {
    'workflow-node-selected': props.selected,
    'workflow-node-dragging': props.node.dragging,
    'workflow-node-resizing': props.node.resizing,
  },
]);

// 节点样式
const nodeStyles = computed(() => {
  const {
    position,
    size = nodeTemplate.value?.defaultSize || { width: 120, height: 60 },
  } = props.node;
  const { properties } = props.node;

  return {
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${size.width}px`,
    height: `${size.height}px`,
    backgroundColor:
      properties.backgroundColor || nodeTemplate.value?.color || '#1890ff',
    borderColor:
      properties.borderColor || nodeTemplate.value?.color || '#1890ff',
    transform: `scale(${props.scale})`,
    transformOrigin: 'top left',
  };
});

// 状态样式类
const statusClasses = computed(() => [
  'workflow-node-status',
  `workflow-node-status-${props.node.status}`,
]);

const statusDotClasses = computed(() => [
  'workflow-node-status-dot',
  `workflow-node-status-dot-${props.node.status}`,
]);

// 图标组件
const iconComponent = computed(() => {
  const iconMap = {
    start: PlayCircle,
    end: StopCircle,
    approval: UserCheck,
    condition: GitBranch,
    parallel: Share,
    merge: TableCellsMerge,
    custom: Circle,
  };
  return iconMap[props.node.type] || Circle;
});

// 图标颜色
const iconColor = computed(() => {
  return props.node.properties.color || '#ffffff';
});

// 获取连接点样式类
function getHandleClasses(handle: Handle) {
  return [
    'workflow-handle',
    `workflow-handle-${handle.type}`,
    `workflow-handle-${handle.position}`,
    {
      'workflow-handle-connectable': handle.isConnectable !== false,
    },
  ];
}

// 获取连接点样式
function getHandleStyles(handle: Handle) {
  const {
    position,
    size = nodeTemplate.value?.defaultSize || { width: 120, height: 60 },
  } = props.node;

  let left = 0;
  let top = 0;

  switch (handle.position) {
    case 'bottom': {
      left = size.width / 2 - 6;
      top = size.height - 6;
      break;
    }
    case 'left': {
      left = -6;
      top = size.height / 2 - 6;
      break;
    }
    case 'right': {
      left = size.width - 6;
      top = size.height / 2 - 6;
      break;
    }
    case 'top': {
      left = size.width / 2 - 6; // 6 是连接点的半径
      top = -6;
      break;
    }
  }

  return {
    left: `${left}px`,
    top: `${top}px`,
    ...handle.style,
  };
}

// 事件处理
function handleClick(event: MouseEvent) {
  event.stopPropagation();
  emit('click', props.node, event);
}

function handleDoubleClick(event: MouseEvent) {
  event.stopPropagation();
  emit('double-click', props.node, event);
}

function handleContextMenu(event: MouseEvent) {
  event.preventDefault();
  event.stopPropagation();
  emit('context-menu', props.node, event);
}

function handleMouseDown(event: MouseEvent) {
  emit('mouse-down', props.node, event);
}

function handleMouseUp(event: MouseEvent) {
  emit('mouse-up', props.node, event);
}

function handleHandleMouseDown(handle: Handle, event: MouseEvent) {
  emit('handle-mouse-down', handle, event);
}

function handleHandleMouseUp(handle: Handle, event: MouseEvent) {
  emit('handle-mouse-up', handle, event);
}
</script>

<template>
  <div
    :class="nodeClasses"
    :style="nodeStyles"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @contextmenu="handleContextMenu"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
  >
    <!-- 状态指示器 -->
    <div v-if="node.status" :class="statusClasses">
      <div :class="statusDotClasses"></div>
    </div>

    <!-- 节点内容 -->
    <div class="workflow-node-content">
      <!-- 节点图标 -->
      <div class="workflow-node-icon">
        <component :is="iconComponent" :size="16" :color="iconColor" />
      </div>

      <!-- 节点文本 -->
      <div class="workflow-node-text">
        <div class="workflow-node-title">
          {{ node.properties.name || nodeTemplate?.name || '未命名节点' }}
        </div>
        <div
          v-if="node.properties.description"
          class="workflow-node-description"
        >
          {{ node.properties.description }}
        </div>
      </div>
    </div>

    <!-- 连接点 -->
    <template v-if="node.handles">
      <div
        v-for="handle in node.handles"
        :key="handle.id"
        :class="getHandleClasses(handle)"
        :style="getHandleStyles(handle)"
        @mousedown.stop="handleHandleMouseDown(handle, $event)"
        @mouseup.stop="handleHandleMouseUp(handle, $event)"
      >
        <div class="workflow-handle-dot"></div>
      </div>
    </template>

    <!-- 调试信息（仅开发模式） -->
    <div v-if="showDebugInfo" class="workflow-node-debug">
      <div>ID: {{ node.id }}</div>
      <div>Type: {{ node.type }}</div>
      <div>Position: ({{ node.position.x }}, {{ node.position.y }})</div>
    </div>
  </div>
</template>

<style scoped>
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(0.8);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(0.8);
  }
}

.workflow-node {
  position: absolute;
  cursor: pointer;
  user-select: none;
  border: 2px solid transparent;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transition: all 0.2s ease;
}

.workflow-node:hover {
  box-shadow: 0 4px 16px rgb(0 0 0 / 25%);
  transform: translateY(-2px);
}

.workflow-node-selected {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
}

.workflow-node-dragging {
  z-index: 1000;
  opacity: 0.8;
  transform: scale(1.05);
}

.workflow-node-resizing {
  border-style: dashed;
}

/* 节点内容 */
.workflow-node-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 8px 12px;
  color: white;
}

.workflow-node-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.workflow-node-text {
  flex: 1;
  min-width: 0;
}

.workflow-node-title {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.4;
  white-space: nowrap;
}

.workflow-node-description {
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 10px;
  line-height: 1.2;
  white-space: nowrap;
  opacity: 0.8;
}

/* 状态指示器 */
.workflow-node-status {
  position: absolute;
  top: -6px;
  right: -6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgb(0 0 0 / 20%);
}

.workflow-node-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.workflow-node-status-dot-pending {
  background-color: #faad14;
}

.workflow-node-status-dot-processing {
  background-color: #1890ff;
  animation: pulse 2s infinite;
}

.workflow-node-status-dot-completed {
  background-color: #52c41a;
}

.workflow-node-status-dot-rejected {
  background-color: #f5222d;
}

.workflow-node-status-dot-timeout {
  background-color: #d9d9d9;
}

/* 连接点 */
.workflow-handle {
  position: absolute;
  z-index: 10;
  width: 12px;
  height: 12px;
  cursor: crosshair;
  border-radius: 50%;
}

.workflow-handle-dot {
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 2px solid #1890ff;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.workflow-handle:hover .workflow-handle-dot {
  border-width: 3px;
  transform: scale(1.2);
}

.workflow-handle-source .workflow-handle-dot {
  border-color: #52c41a;
}

.workflow-handle-target .workflow-handle-dot {
  border-color: #f5222d;
}

.workflow-handle-connectable:hover {
  cursor: crosshair;
}

/* 特定节点类型样式 */
.workflow-node-start {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.workflow-node-end {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
}

.workflow-node-approval {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.workflow-node-condition {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
  transform: rotate(45deg);
}

.workflow-node-condition .workflow-node-content {
  transform: rotate(-45deg);
}

.workflow-node-parallel {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.workflow-node-merge {
  background: linear-gradient(135deg, #13c2c2, #36cfc9);
}

.workflow-node-custom {
  background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
}

/* 调试信息 */
.workflow-node-debug {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  padding: 4px 8px;
  margin-top: 4px;
  font-size: 10px;
  color: white;
  white-space: nowrap;
  background: rgb(0 0 0 / 80%);
  border-radius: 4px;
}

.workflow-node-debug div {
  margin: 1px 0;
}
</style>
