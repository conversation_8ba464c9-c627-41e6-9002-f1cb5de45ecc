<script setup lang="ts">
import type {
  Approver,
  ConditionRule,
  WorkflowEdge,
  WorkflowNode,
} from '../types/workflow';

import { computed, ref, watch } from 'vue';

import {
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  RadioGroup,
  Row,
  Select,
  Space,
  Textarea,
} from 'ant-design-vue';
import { Delete, Plus } from 'lucide-vue-next';

import { CONDITION_OPERATORS } from '../config/nodes';

interface Props {
  element: WorkflowEdge | WorkflowNode;
  elementType: 'edge' | 'node';
  approverDataSource?: Approver[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  update: [element: WorkflowEdge | WorkflowNode];
}>();

// 响应式数据
const nodeForm = ref({
  name: '',
  description: '',
  approvers: [] as string[],
  approvalType: 'single',
  timeLimit: 24,
  autoApprove: false,
  conditions: [] as ConditionRule[],
  branches: [] as string[],
});

const edgeForm = ref({
  label: '',
  condition: '',
  description: '',
  priority: 'normal' as 'high' | 'low' | 'normal' | 'urgent',
  style: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: '',
    animation: false,
  },
  validation: {
    required: false,
    message: '',
  },
  tags: [] as string[],
});

// 计算属性
const nodeElement = computed(() =>
  props.elementType === 'node' ? (props.element as WorkflowNode) : null,
);

const edgeElement = computed(() =>
  props.elementType === 'edge' ? (props.element as WorkflowEdge) : null,
);

const approverOptions = computed(
  () =>
    props.approverDataSource?.map((approver) => ({
      label: approver.name,
      value: approver.id,
    })) || [],
);

const operatorOptions = computed(() =>
  CONDITION_OPERATORS.map((op) => ({
    label: op.label,
    value: op.value,
  })),
);

// 监听元素变化
watch(
  () => props.element,
  (newElement) => {
    if (props.elementType === 'node') {
      const node = newElement as WorkflowNode;
      nodeForm.value = {
        name: node.properties.name || '',
        description: node.properties.description || '',
        approvers: node.properties.approvers?.map((a) => a.id) || [],
        approvalType: node.properties.approvalType || 'single',
        timeLimit: node.properties.timeLimit || 24,
        autoApprove: node.properties.autoApprove || false,
        conditions: node.properties.conditions || [],
        branches: node.properties.branches || ['分支1', '分支2'],
      };
    } else {
      const edge = newElement as WorkflowEdge;
      edgeForm.value = {
        label: edge.properties?.label || '',
        condition: edge.properties?.condition || '',
        description: edge.properties?.description || '',
        priority: edge.properties?.priority || 'normal',
        style: {
          stroke: edge.properties?.style?.stroke || '#1890ff',
          strokeWidth: edge.properties?.style?.strokeWidth || 2,
          strokeDasharray: edge.properties?.style?.strokeDasharray || '',
          animation: edge.properties?.style?.animation || false,
        },
        validation: {
          required: edge.properties?.validation?.required || false,
          message: edge.properties?.validation?.message || '',
        },
        tags: edge.properties?.tags || [],
      };
    }
  },
  { immediate: true },
);

// 方法
const handleUpdate = () => {
  if (props.elementType === 'node') {
    const node = { ...nodeElement.value! };
    node.properties = {
      ...node.properties,
      name: nodeForm.value.name,
      description: nodeForm.value.description,
    };

    if (node.type === 'approval') {
      node.properties.approvers = nodeForm.value.approvers.map((id) => {
        const approver = props.approverDataSource?.find((a) => a.id === id);
        return approver || { id, name: id };
      });
      node.properties.approvalType = nodeForm.value.approvalType as
        | 'all'
        | 'majority'
        | 'single';
      node.properties.timeLimit = nodeForm.value.timeLimit;
      node.properties.autoApprove = nodeForm.value.autoApprove;
    }

    if (node.type === 'condition') {
      node.properties.conditions = nodeForm.value.conditions;
    }

    if (node.type === 'parallel') {
      node.properties.branches = nodeForm.value.branches;
    }

    emit('update', node);
  } else {
    const edge = { ...edgeElement.value! };
    edge.properties = {
      ...edge.properties,
      label: edgeForm.value.label,
      condition: edgeForm.value.condition,
      description: edgeForm.value.description,
      priority: edgeForm.value.priority,
      style: edgeForm.value.style,
      validation: edgeForm.value.validation,
      tags: edgeForm.value.tags,
    };
    emit('update', edge);
  }
};

const addCondition = () => {
  nodeForm.value.conditions.push({
    field: '',
    operator: 'eq',
    value: '',
  });
  handleUpdate();
};

const removeCondition = (index: number) => {
  nodeForm.value.conditions.splice(index, 1);
  handleUpdate();
};

const addBranch = () => {
  const branchCount = nodeForm.value.branches.length;
  nodeForm.value.branches.push(`分支${branchCount + 1}`);
  handleUpdate();
};

const removeBranch = (index: number) => {
  nodeForm.value.branches.splice(index, 1);
  handleUpdate();
};

const handleReset = () => {
  // 重置表单到初始状态
  watch(
    () => props.element,
    () => {},
    { immediate: true },
  );
  message.info('已重置');
};

const handleSave = () => {
  handleUpdate();
  message.success('保存成功');
};
</script>

<template>
  <div class="workflow-property-panel">
    <!-- 节点属性 -->
    <div v-if="elementType === 'node'" class="property-content">
      <Form :model="nodeForm" layout="vertical">
        <!-- 基础属性 -->
        <Form.Item label="节点名称">
          <Input
            v-model:value="nodeForm.name"
            placeholder="请输入节点名称"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="节点描述">
          <Textarea
            v-model:value="nodeForm.description"
            placeholder="请输入节点描述"
            :rows="3"
            @change="handleUpdate"
          />
        </Form.Item>

        <!-- 审批节点特有属性 -->
        <template v-if="nodeElement?.type === 'approval'">
          <Divider>审批设置</Divider>

          <Form.Item label="审批人">
            <Select
              v-model:value="nodeForm.approvers"
              mode="multiple"
              placeholder="请选择审批人"
              :options="approverOptions"
              @change="handleUpdate"
            />
          </Form.Item>

          <Form.Item label="审批类型">
            <RadioGroup
              v-model:value="nodeForm.approvalType"
              @change="handleUpdate"
            >
              <Radio value="single">单人审批</Radio>
              <Radio value="all">全部审批</Radio>
              <Radio value="majority">多数审批</Radio>
            </RadioGroup>
          </Form.Item>

          <Form.Item label="审批时限（小时）">
            <InputNumber
              v-model:value="nodeForm.timeLimit"
              :min="1"
              :max="720"
              placeholder="24"
              style="width: 100%"
              @change="handleUpdate"
            />
          </Form.Item>

          <Form.Item>
            <Checkbox
              v-model:checked="nodeForm.autoApprove"
              @change="handleUpdate"
            >
              超时自动审批
            </Checkbox>
          </Form.Item>
        </template>

        <!-- 条件节点特有属性 -->
        <template v-if="nodeElement?.type === 'condition'">
          <Divider>条件设置</Divider>

          <div class="condition-rules">
            <div
              v-for="(condition, index) in nodeForm.conditions"
              :key="index"
              class="condition-rule"
            >
              <Row :gutter="8">
                <Col :span="8">
                  <Input
                    v-model:value="condition.field"
                    placeholder="字段名"
                    @change="handleUpdate"
                  />
                </Col>
                <Col :span="6">
                  <Select
                    v-model:value="condition.operator"
                    placeholder="操作符"
                    :options="operatorOptions"
                    @change="handleUpdate"
                  />
                </Col>
                <Col :span="8">
                  <Input
                    v-model:value="condition.value"
                    placeholder="值"
                    @change="handleUpdate"
                  />
                </Col>
                <Col :span="2">
                  <Button
                    type="text"
                    danger
                    size="small"
                    @click="removeCondition(index)"
                  >
                    <template #icon><Delete class="h-4 w-4" /></template>
                  </Button>
                </Col>
              </Row>
            </div>

            <Button type="dashed" block @click="addCondition">
              <template #icon><Plus class="h-4 w-4" /></template>
              添加条件
            </Button>
          </div>
        </template>

        <!-- 并行节点特有属性 -->
        <template v-if="nodeElement?.type === 'parallel'">
          <Divider>并行设置</Divider>

          <div class="branch-settings">
            <div
              v-for="(branch, index) in nodeForm.branches"
              :key="index"
              class="branch-item"
            >
              <Input
                v-model:value="nodeForm.branches[index]"
                :placeholder="`分支 ${index + 1}`"
                @change="handleUpdate"
              />
              <Button
                v-if="nodeForm.branches.length > 2"
                type="text"
                danger
                size="small"
                @click="removeBranch(index)"
              >
                <template #icon><Delete /></template>
              </Button>
            </div>

            <Button type="dashed" block @click="addBranch">
              <template #icon><Plus /></template>
              添加分支
            </Button>
          </div>
        </template>
      </Form>
    </div>

    <!-- 连线属性 -->
    <div v-else-if="elementType === 'edge'" class="property-content">
      <Form :model="edgeForm" layout="vertical">
        <Form.Item label="连线标签">
          <Input
            v-model:value="edgeForm.label"
            placeholder="请输入连线标签"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="连线描述">
          <Textarea
            v-model:value="edgeForm.description"
            placeholder="请输入连线描述"
            :rows="2"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="条件表达式">
          <Textarea
            v-model:value="edgeForm.condition"
            placeholder="请输入条件表达式"
            :rows="3"
            @change="handleUpdate"
          />
        </Form.Item>

        <Divider>样式设置</Divider>

        <Form.Item label="优先级">
          <Select
            v-model:value="edgeForm.priority"
            placeholder="请选择优先级"
            @change="handleUpdate"
          >
            <Select.Option value="low">低</Select.Option>
            <Select.Option value="normal">普通</Select.Option>
            <Select.Option value="high">高</Select.Option>
            <Select.Option value="urgent">紧急</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="线条颜色">
          <Input
            v-model:value="edgeForm.style.stroke"
            placeholder="#1890ff"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="线条宽度">
          <InputNumber
            v-model:value="edgeForm.style.strokeWidth"
            :min="1"
            :max="10"
            placeholder="2"
            style="width: 100%"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="线条样式">
          <Select
            v-model:value="edgeForm.style.strokeDasharray"
            placeholder="请选择线条样式"
            @change="handleUpdate"
          >
            <Select.Option value="">实线</Select.Option>
            <Select.Option value="5,5">虚线</Select.Option>
            <Select.Option value="2,2">点线</Select.Option>
            <Select.Option value="10,5,2,5">点划线</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Checkbox
            v-model:checked="edgeForm.style.animation"
            @change="handleUpdate"
          >
            启用动画效果
          </Checkbox>
        </Form.Item>

        <Divider>验证设置</Divider>

        <Form.Item>
          <Checkbox
            v-model:checked="edgeForm.validation.required"
            @change="handleUpdate"
          >
            必须经过此连线
          </Checkbox>
        </Form.Item>

        <Form.Item v-if="edgeForm.validation.required" label="验证消息">
          <Input
            v-model:value="edgeForm.validation.message"
            placeholder="请输入验证失败时的消息"
            @change="handleUpdate"
          />
        </Form.Item>

        <Form.Item label="标签">
          <Select
            v-model:value="edgeForm.tags"
            mode="tags"
            placeholder="请输入标签"
            @change="handleUpdate"
          />
        </Form.Item>
      </Form>
    </div>

    <!-- 操作按钮 -->
    <div class="property-actions">
      <Space>
        <Button @click="handleReset">重置</Button>
        <Button type="primary" @click="handleSave">保存</Button>
      </Space>
    </div>
  </div>
</template>

<style scoped>
.workflow-property-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.property-content {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.condition-rules {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-rule {
  padding: 8px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.branch-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.branch-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.property-actions {
  padding: 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-size: 13px;
  font-weight: 500;
}

:deep(.ant-divider) {
  margin: 16px 0 12px;
  font-size: 12px;
  color: #8c8c8c;
}
</style>
