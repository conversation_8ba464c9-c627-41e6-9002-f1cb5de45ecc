<script setup lang="ts">
import type {
  Approver,
  NodeTemplate,
  ValidationResult,
  WorkflowConfig,
  WorkflowData,
  WorkflowEdge,
  WorkflowInstance,
  WorkflowNode,
  WorkflowTemplate,
} from './types';

import { computed, nextTick, reactive, ref, watch } from 'vue';

import { Button, message, Space } from 'ant-design-vue';
import {
  Download,
  Eye,
  Redo,
  RotateCcw,
  Trash2,
  Undo,
  Upload,
  ZoomIn,
  ZoomOut,
} from 'lucide-vue-next';

import WorkflowEdgeComponent from './components/edges/WorkflowEdge.vue';
import WorkflowNodeComponent from './components/nodes/WorkflowNode.vue';
import PropertyPanel from './components/panels/PropertyPanel.vue';
import {
  DEFAULT_APPROVERS,
  DEFAULT_NODE_TEMPLATES,
  DEFAULT_WORKFLOW_CONFIG,
  WORKFLOW_TEMPLATES,
} from './config';
import {
  cloneWorkflowData,
  exportWorkflowData,
  generateId,
  getNodeBounds,
  importWorkflowData,
  validateWorkflow,
} from './utils';

interface Props {
  modelValue?: WorkflowData;
  config?: Partial<WorkflowConfig>;
  nodeTemplates?: NodeTemplate[];
  workflowTemplates?: WorkflowTemplate[];
  approverDataSource?: Approver[];
  readonly?: boolean;
  height?: number | string;
  width?: number | string;
  showNodePanel?: boolean;
  showDebugInfo?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: WorkflowData): void;
  (e: 'nodeClick', node: WorkflowNode): void;
  (e: 'nodeDoubleClick', node: WorkflowNode): void;
  (e: 'nodeContextMenu', node: WorkflowNode, event: MouseEvent): void;
  (e: 'edgeClick', edge: WorkflowEdge): void;
  (e: 'edgeContextMenu', edge: WorkflowEdge, event: MouseEvent): void;
  (
    e: 'selectionChange',
    selection: { edges: WorkflowEdge[]; nodes: WorkflowNode[] },
  ): void;
  (e: 'validate', result: ValidationResult): void;
  (e: 'canvasClick', event: MouseEvent): void;
  (e: 'canvasContextMenu', event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ nodes: [], edges: [] }),
  config: () => ({}),
  nodeTemplates: () => DEFAULT_NODE_TEMPLATES,
  workflowTemplates: () => WORKFLOW_TEMPLATES,
  approverDataSource: () => DEFAULT_APPROVERS,
  readonly: false,
  height: '100vh',
  width: '100%',
  showNodePanel: true,
  showDebugInfo: false,
});

const emit = defineEmits<Emits>();

// 响应式数据
const canvasRef = ref<HTMLElement>();
const workflowData = reactive<WorkflowData>(
  cloneWorkflowData(props.modelValue),
);
const selectedNodes = ref<string[]>([]);
const selectedEdges = ref<string[]>([]);
const viewport = reactive({
  x: 0,
  y: 0,
  zoom: 1,
});

// 历史记录
const history = ref<WorkflowData[]>([]);
const historyIndex = ref(-1);

// 交互状态
const isDragging = ref(false);
const isConnecting = ref(false);
const isPanning = ref(false);

// 验证结果
const validationResult = ref<ValidationResult>({
  valid: true,
  errors: [],
  warnings: [],
});

// 合并配置
const mergedConfig = computed<WorkflowConfig>(() => ({
  ...DEFAULT_WORKFLOW_CONFIG,
  ...props.config,
}));

// 画布样式
const canvasStyles = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  cursor: isPanning.value
    ? 'grabbing'
    : isConnecting.value
      ? 'crosshair'
      : 'default',
}));

const canvasSize = computed(() => ({
  width: typeof props.width === 'number' ? props.width : 1200,
  height: typeof props.height === 'number' ? props.height : 800,
}));

const svgStyles = computed(() => ({
  transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
  transformOrigin: '0 0',
}));

const nodesLayerStyles = computed(() => ({
  transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
  transformOrigin: '0 0',
}));

// 计算属性
const canUndo = computed(() => historyIndex.value > 0);
const canRedo = computed(() => historyIndex.value < history.value.length - 1);

// 工具方法
function getNodeById(id: string): undefined | WorkflowNode {
  return workflowData.nodes.find((node) => node.id === id);
}

function getEdgeById(id: string): undefined | WorkflowEdge {
  return workflowData.edges.find((edge) => edge.id === id);
}

function getSelectedNodes(): WorkflowNode[] {
  return workflowData.nodes.filter((node) =>
    selectedNodes.value.includes(node.id),
  );
}

function getSelectedEdges(): WorkflowEdge[] {
  return workflowData.edges.filter((edge) =>
    selectedEdges.value.includes(edge.id),
  );
}

// 历史记录管理
function saveToHistory() {
  const currentData = cloneWorkflowData(workflowData);
  history.value.splice(historyIndex.value + 1);
  history.value.push(currentData);
  historyIndex.value = history.value.length - 1;

  // 限制历史记录数量
  if (history.value.length > 50) {
    history.value.shift();
    historyIndex.value--;
  }
}

function restoreFromHistory(index: number) {
  if (index >= 0 && index < history.value.length) {
    const data = cloneWorkflowData(history.value[index]);
    Object.assign(workflowData, data);
    historyIndex.value = index;
    emit('update:modelValue', workflowData);
  }
}

// 事件处理器
function handleUndo() {
  if (canUndo.value) {
    restoreFromHistory(historyIndex.value - 1);
  }
}

function handleRedo() {
  if (canRedo.value) {
    restoreFromHistory(historyIndex.value + 1);
  }
}

function handleZoomIn() {
  const newZoom = Math.min(
    viewport.zoom * 1.2,
    mergedConfig.value.zoom?.max || 2,
  );
  viewport.zoom = newZoom;
}

function handleZoomOut() {
  const newZoom = Math.max(
    viewport.zoom / 1.2,
    mergedConfig.value.zoom?.min || 0.1,
  );
  viewport.zoom = newZoom;
}

function handleZoomToFit() {
  if (workflowData.nodes.length === 0) return;

  const bounds = getNodeBounds(workflowData.nodes);
  const padding = 50;
  const canvas = canvasSize.value;

  const scaleX = (canvas.width - padding * 2) / bounds.width;
  const scaleY = (canvas.height - padding * 2) / bounds.height;
  const scale = Math.min(scaleX, scaleY, mergedConfig.value.zoom?.max || 2);

  viewport.zoom = scale;
  viewport.x = padding - bounds.x * scale;
  viewport.y = padding - bounds.y * scale;
}

function handleValidate() {
  const result = validateWorkflow(workflowData);
  validationResult.value = result;

  if (result.valid) {
    message.success('工作流验证通过');
  } else {
    message.error(`工作流验证失败: ${result.errors.length} 个错误`);
  }

  emit('validate', result);
}

function handleExport() {
  const data = exportWorkflowData(workflowData, true);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `workflow-${Date.now()}.json`;
  document.body.append(a);
  a.click();
  a.remove();
  URL.revokeObjectURL(url);
  message.success('工作流已导出');
}

function handleImport() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.addEventListener('change', (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.addEventListener('load', (e) => {
        try {
          const data = JSON.parse(e.target?.result as string);
          saveToHistory();
          Object.assign(workflowData, data);
          emit('update:modelValue', workflowData);
          message.success('工作流已导入');
        } catch {
          message.error('导入失败：文件格式错误');
        }
      });
      reader.readAsText(file);
    }
  });
  input.click();
}

function handleClear() {
  saveToHistory();
  workflowData.nodes = [];
  workflowData.edges = [];
  selectedNodes.value = [];
  selectedEdges.value = [];
  emit('update:modelValue', workflowData);
  message.success('画布已清空');
}

// 节点相关事件
function handleNodeClick(node: WorkflowNode, event: MouseEvent) {
  if (!event.ctrlKey && !event.metaKey) {
    selectedNodes.value = [node.id];
    selectedEdges.value = [];
  } else {
    const index = selectedNodes.value.indexOf(node.id);
    if (index === -1) {
      selectedNodes.value.push(node.id);
    } else {
      selectedNodes.value.splice(index, 1);
    }
  }

  emitSelectionChange();
  emit('nodeClick', node);
}

function handleNodeDoubleClick(node: WorkflowNode, _event: MouseEvent) {
  emit('nodeDoubleClick', node);
}

function handleNodeContextMenu(node: WorkflowNode, event: MouseEvent) {
  emit('nodeContextMenu', node, event);
}

function handleNodeMouseDown(node: WorkflowNode, event: MouseEvent) {
  if (event.button === 0) {
    // 左键
    isDragging.value = true;
  }
}

function handleNodeMouseUp(_node: WorkflowNode, _event: MouseEvent) {
  isDragging.value = false;
}

// 连线相关事件
function handleEdgeClick(edge: WorkflowEdge, event: MouseEvent) {
  if (!event.ctrlKey && !event.metaKey) {
    selectedEdges.value = [edge.id];
    selectedNodes.value = [];
  } else {
    const index = selectedEdges.value.indexOf(edge.id);
    if (index === -1) {
      selectedEdges.value.push(edge.id);
    } else {
      selectedEdges.value.splice(index, 1);
    }
  }

  emitSelectionChange();
  emit('edgeClick', edge);
}

function handleEdgeDoubleClick(_edge: WorkflowEdge, _event: MouseEvent) {
  // 双击连线可以添加标签或编辑属性
}

function handleEdgeContextMenu(edge: WorkflowEdge, event: MouseEvent) {
  emit('edgeContextMenu', edge, event);
}

// 画布相关事件
function handleCanvasClick(event: MouseEvent) {
  if (!isDragging.value && !isConnecting.value) {
    selectedNodes.value = [];
    selectedEdges.value = [];
    emitSelectionChange();
  }

  emit('canvasClick', event);
}

function handleCanvasContextMenu(event: MouseEvent) {
  emit('canvasContextMenu', event);
}

function handleCanvasMouseDown(event: MouseEvent) {
  if (event.button === 1 || (event.button === 0 && event.altKey)) {
    // 中键或Alt+左键
    isPanning.value = true;
  }
}

function handleCanvasMouseMove(event: MouseEvent) {
  if (isPanning.value) {
    // 处理画布平移
    viewport.x += event.movementX;
    viewport.y += event.movementY;
  }
}

function handleCanvasMouseUp(_event: MouseEvent) {
  isPanning.value = false;
  isDragging.value = false;
}

function handleCanvasWheel(event: WheelEvent) {
  event.preventDefault();

  const delta = event.deltaY > 0 ? 0.9 : 1.1;
  const newZoom = Math.max(
    mergedConfig.value.zoom?.min || 0.1,
    Math.min(mergedConfig.value.zoom?.max || 2, viewport.zoom * delta),
  );

  // 以鼠标位置为中心缩放
  const rect = canvasRef.value?.getBoundingClientRect();
  if (rect) {
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    const scaleRatio = newZoom / viewport.zoom;
    viewport.x = mouseX - (mouseX - viewport.x) * scaleRatio;
    viewport.y = mouseY - (mouseY - viewport.y) * scaleRatio;
    viewport.zoom = newZoom;
  }
}

function emitSelectionChange() {
  const nodes = getSelectedNodes();
  const edges = getSelectedEdges();
  emit('selectionChange', { nodes, edges });
}

// 属性更新
function handleNodeUpdate(nodeId: string, updates: Partial<WorkflowNode>) {
  const node = getNodeById(nodeId);
  if (node) {
    Object.assign(node, updates);
    emit('update:modelValue', workflowData);
  }
}

function handleEdgeUpdate(edgeId: string, updates: Partial<WorkflowEdge>) {
  const edge = getEdgeById(edgeId);
  if (edge) {
    Object.assign(edge, updates);
    emit('update:modelValue', workflowData);
  }
}

function handleClearSelection() {
  selectedNodes.value = [];
  selectedEdges.value = [];
  emitSelectionChange();
}

function handleDeleteElements() {
  saveToHistory();

  // 删除选中的节点
  workflowData.nodes = workflowData.nodes.filter(
    (node) => !selectedNodes.value.includes(node.id),
  );

  // 删除选中的连线以及连接到被删除节点的连线
  workflowData.edges = workflowData.edges.filter(
    (edge) =>
      !selectedEdges.value.includes(edge.id) &&
      !selectedNodes.value.includes(edge.sourceNodeId) &&
      !selectedNodes.value.includes(edge.targetNodeId),
  );

  selectedNodes.value = [];
  selectedEdges.value = [];

  emit('update:modelValue', workflowData);
  emitSelectionChange();
  message.success('已删除选中元素');
}

// 创建示例节点用于测试
function createSampleNodes() {
  saveToHistory();

  const startNode: WorkflowNode = {
    id: generateId(),
    type: 'start',
    position: { x: 100, y: 100 },
    properties: { name: '开始' },
    size: { width: 120, height: 60 },
  };

  const approvalNode: WorkflowNode = {
    id: generateId(),
    type: 'approval',
    position: { x: 300, y: 100 },
    properties: { name: '审批节点' },
    size: { width: 120, height: 60 },
  };

  const endNode: WorkflowNode = {
    id: generateId(),
    type: 'end',
    position: { x: 500, y: 100 },
    properties: { name: '结束' },
    size: { width: 120, height: 60 },
  };

  const edge1: WorkflowEdge = {
    id: generateId(),
    type: 'bezier',
    sourceNodeId: startNode.id,
    targetNodeId: approvalNode.id,
    properties: {},
  };

  const edge2: WorkflowEdge = {
    id: generateId(),
    type: 'bezier',
    sourceNodeId: approvalNode.id,
    targetNodeId: endNode.id,
    properties: {},
  };

  workflowData.nodes.push(startNode, approvalNode, endNode);
  workflowData.edges.push(edge1, edge2);

  emit('update:modelValue', workflowData);
  message.success('已创建示例工作流');
}

// 暴露实例方法
const instance: WorkflowInstance = {
  getWorkflowData: () => cloneWorkflowData(workflowData),
  setWorkflowData: (data: WorkflowData) => {
    Object.assign(workflowData, data);
    emit('update:modelValue', workflowData);
  },
  addNode: (node: Omit<WorkflowNode, 'id'>) => {
    const newNode = { ...node, id: generateId() };
    workflowData.nodes.push(newNode);
    emit('update:modelValue', workflowData);
    return newNode.id;
  },
  updateNode: (id: string, updates: Partial<WorkflowNode>) =>
    handleNodeUpdate(id, updates),
  deleteNode: (id: string) => {
    workflowData.nodes = workflowData.nodes.filter((node) => node.id !== id);
    workflowData.edges = workflowData.edges.filter(
      (edge) => edge.sourceNodeId !== id && edge.targetNodeId !== id,
    );
    emit('update:modelValue', workflowData);
  },
  getNode: (id: string) => getNodeById(id),
  addEdge: (edge: Omit<WorkflowEdge, 'id'>) => {
    const newEdge = { ...edge, id: generateId() };
    workflowData.edges.push(newEdge);
    emit('update:modelValue', workflowData);
    return newEdge.id;
  },
  updateEdge: (id: string, updates: Partial<WorkflowEdge>) =>
    handleEdgeUpdate(id, updates),
  deleteEdge: (id: string) => {
    workflowData.edges = workflowData.edges.filter((edge) => edge.id !== id);
    emit('update:modelValue', workflowData);
  },
  getEdge: (id: string) => getEdgeById(id),
  selectNode: (id: string) => {
    selectedNodes.value = [id];
    selectedEdges.value = [];
    emitSelectionChange();
  },
  selectEdge: (id: string) => {
    selectedEdges.value = [id];
    selectedNodes.value = [];
    emitSelectionChange();
  },
  selectAll: () => {
    selectedNodes.value = workflowData.nodes.map((node) => node.id);
    selectedEdges.value = workflowData.edges.map((edge) => edge.id);
    emitSelectionChange();
  },
  clearSelection: handleClearSelection,
  getSelection: () => ({
    nodes: getSelectedNodes(),
    edges: getSelectedEdges(),
  }),
  zoomIn: handleZoomIn,
  zoomOut: handleZoomOut,
  zoomToFit: handleZoomToFit,
  zoomToSelection: () => {
    const selectedNodesData = getSelectedNodes();
    if (selectedNodesData.length > 0) {
      const bounds = getNodeBounds(selectedNodesData);
      const padding = 50;
      const canvas = canvasSize.value;

      const scaleX = (canvas.width - padding * 2) / bounds.width;
      const scaleY = (canvas.height - padding * 2) / bounds.height;
      const scale = Math.min(scaleX, scaleY, mergedConfig.value.zoom?.max || 2);

      viewport.zoom = scale;
      viewport.x = padding - bounds.x * scale;
      viewport.y = padding - bounds.y * scale;
    }
  },
  centerView: () => {
    viewport.x = 0;
    viewport.y = 0;
  },
  validate: () => {
    const result = validateWorkflow(workflowData);
    validationResult.value = result;
    return result;
  },
  exportData: (format = 'json') =>
    exportWorkflowData(workflowData, format === 'json'),
  importData: (data: string | WorkflowData) => {
    let parsedData: null | WorkflowData = null;

    parsedData = typeof data === 'string' ? importWorkflowData(data) : data;

    if (parsedData) {
      saveToHistory();
      Object.assign(workflowData, parsedData);
      emit('update:modelValue', workflowData);
      return true;
    }
    return false;
  },
  undo: handleUndo,
  redo: handleRedo,
  canUndo: () => canUndo.value,
  canRedo: () => canRedo.value,
};

// 监听数据变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(workflowData, cloneWorkflowData(newValue));
    }
  },
  { deep: true },
);

// 初始化历史记录
nextTick(() => {
  saveToHistory();
});

// 暴露组件实例
defineExpose(instance);
</script>

<template>
  <div class="workflow-designer" @contextmenu.prevent>
    <div class="workflow-designer-container">
      <!-- 简化的工具栏 -->
      <div class="workflow-toolbar">
        <div class="toolbar-section">
          <Space>
            <Button size="small" :disabled="!canUndo" @click="handleUndo">
              <template #icon><Undo :size="16" /></template>
              撤销
            </Button>
            <Button size="small" :disabled="!canRedo" @click="handleRedo">
              <template #icon><Redo :size="16" /></template>
              重做
            </Button>
          </Space>
        </div>

        <div class="toolbar-section">
          <Space>
            <Button size="small" @click="handleZoomIn">
              <template #icon><ZoomIn :size="16" /></template>
              放大
            </Button>
            <Button size="small" @click="handleZoomOut">
              <template #icon><ZoomOut :size="16" /></template>
              缩小
            </Button>
            <Button size="small" @click="handleZoomToFit">
              <template #icon><RotateCcw :size="16" /></template>
              适应画布
            </Button>
          </Space>
        </div>

        <div class="toolbar-section">
          <Space>
            <Button size="small" @click="handleValidate">
              <template #icon><Eye :size="16" /></template>
              验证
            </Button>
            <Button size="small" @click="createSampleNodes"> 创建示例 </Button>
          </Space>
        </div>

        <div class="toolbar-section">
          <Space>
            <Button size="small" @click="handleExport">
              <template #icon><Download :size="16" /></template>
              导出
            </Button>
            <Button size="small" @click="handleImport">
              <template #icon><Upload :size="16" /></template>
              导入
            </Button>
            <Button size="small" danger @click="handleClear">
              <template #icon><Trash2 :size="16" /></template>
              清空
            </Button>
          </Space>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="workflow-content">
        <!-- 画布区域 -->
        <div class="workflow-canvas-container">
          <div
            ref="canvasRef"
            class="workflow-canvas"
            :style="canvasStyles"
            @click="handleCanvasClick"
            @contextmenu="handleCanvasContextMenu"
            @wheel="handleCanvasWheel"
            @mousedown="handleCanvasMouseDown"
            @mousemove="handleCanvasMouseMove"
            @mouseup="handleCanvasMouseUp"
          >
            <!-- 网格背景 -->
            <div class="workflow-background">
              <svg class="grid-pattern" width="100%" height="100%">
                <defs>
                  <pattern
                    id="grid"
                    width="20"
                    height="20"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M 20 0 L 0 0 0 20"
                      fill="none"
                      stroke="#f0f0f0"
                      stroke-width="1"
                    />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>

            <!-- SVG 画布 -->
            <svg
              class="workflow-svg"
              :width="canvasSize.width"
              :height="canvasSize.height"
              :style="svgStyles"
            >
              <!-- 定义箭头标记 -->
              <defs>
                <marker
                  id="arrow-marker"
                  markerWidth="10"
                  markerHeight="10"
                  refX="8"
                  refY="3"
                  orient="auto"
                  markerUnits="strokeWidth"
                >
                  <path d="M0,0 L0,6 L9,3 z" fill="#8c8c8c" />
                </marker>
              </defs>

              <!-- 连线 -->
              <g class="workflow-edges">
                <WorkflowEdgeComponent
                  v-for="edge in workflowData.edges"
                  :key="edge.id"
                  :edge="edge"
                  :source-node="getNodeById(edge.sourceNodeId)!"
                  :target-node="getNodeById(edge.targetNodeId)!"
                  :selected="selectedEdges.includes(edge.id)"
                  :scale="viewport.zoom"
                  @click="handleEdgeClick"
                  @double-click="handleEdgeDoubleClick"
                  @context-menu="handleEdgeContextMenu"
                />
              </g>
            </svg>

            <!-- 节点层 -->
            <div class="workflow-nodes" :style="nodesLayerStyles">
              <WorkflowNodeComponent
                v-for="node in workflowData.nodes"
                :key="node.id"
                :node="node"
                :selected="selectedNodes.includes(node.id)"
                :scale="viewport.zoom"
                :show-debug-info="showDebugInfo"
                @click="handleNodeClick"
                @double-click="handleNodeDoubleClick"
                @context-menu="handleNodeContextMenu"
                @mouse-down="handleNodeMouseDown"
                @mouse-up="handleNodeMouseUp"
              />
            </div>
          </div>
        </div>

        <!-- 属性面板 -->
        <PropertyPanel
          :selected-nodes="getSelectedNodes()"
          :selected-edges="getSelectedEdges()"
          :approver-data-source="approverDataSource"
          @node-update="handleNodeUpdate"
          @edge-update="handleEdgeUpdate"
          @clear-selection="handleClearSelection"
          @delete-elements="handleDeleteElements"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.workflow-designer {
  position: relative;
  width: 100%;
  height: 100%;
  user-select: none;
  background: #fafafa;
}

.workflow-designer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.workflow-toolbar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-section {
  display: flex;
  align-items: center;
}

.workflow-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.workflow-canvas-container {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.workflow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.workflow-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.grid-pattern {
  width: 100%;
  height: 100%;
}

.workflow-svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.workflow-svg .workflow-edges {
  pointer-events: auto;
}

.workflow-nodes {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.workflow-nodes > * {
  pointer-events: auto;
}
</style>
