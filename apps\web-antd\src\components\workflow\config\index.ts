import type { NodeTemplate, WorkflowConfig, WorkflowTemplate } from '../types';

/**
 * 默认节点模板配置
 */
export const DEFAULT_NODE_TEMPLATES: NodeTemplate[] = [
  {
    type: 'start',
    name: '开始',
    description: '工作流开始节点',
    icon: 'play-circle',
    color: '#52c41a',
    defaultProperties: {
      name: '开始',
      description: '工作流的起始节点',
    },
    defaultSize: {
      width: 80,
      height: 40,
    },
    handles: [
      {
        id: 'start-output',
        type: 'source',
        position: 'right',
        isConnectable: true,
      },
    ],
  },
  {
    type: 'end',
    name: '结束',
    description: '工作流结束节点',
    icon: 'stop-circle',
    color: '#f5222d',
    defaultProperties: {
      name: '结束',
      description: '工作流的结束节点',
    },
    defaultSize: {
      width: 80,
      height: 40,
    },
    handles: [
      {
        id: 'end-input',
        type: 'target',
        position: 'left',
        isConnectable: true,
      },
    ],
  },
  {
    type: 'approval',
    name: '审批',
    description: '审批节点，需要人工审批',
    icon: 'user-check',
    color: '#1890ff',
    defaultProperties: {
      name: '审批节点',
      description: '需要审批人处理的节点',
      approvers: [],
      approvalType: 'single',
      timeLimit: 24,
      autoApprove: false,
      escalation: false,
      escalationTime: 48,
      escalationApprovers: [],
    },
    defaultSize: {
      width: 120,
      height: 60,
    },
    handles: [
      {
        id: 'approval-input',
        type: 'target',
        position: 'left',
        isConnectable: true,
      },
      {
        id: 'approval-output',
        type: 'source',
        position: 'right',
        isConnectable: true,
      },
    ],
  },
  {
    type: 'condition',
    name: '条件',
    description: '条件判断节点，根据条件分支',
    icon: 'git-branch',
    color: '#fa8c16',
    defaultProperties: {
      name: '条件节点',
      description: '根据条件进行分支判断',
      conditions: [],
      defaultPath: 'true',
    },
    defaultSize: {
      width: 100,
      height: 80,
    },
    handles: [
      {
        id: 'condition-input',
        type: 'target',
        position: 'left',
        isConnectable: true,
      },
      {
        id: 'condition-output-true',
        type: 'source',
        position: 'right',
        isConnectable: true,
      },
      {
        id: 'condition-output-false',
        type: 'source',
        position: 'bottom',
        isConnectable: true,
      },
    ],
  },
  {
    type: 'parallel',
    name: '并行',
    description: '并行处理节点',
    icon: 'share',
    color: '#722ed1',
    defaultProperties: {
      name: '并行节点',
      description: '同时执行多个分支',
      branches: ['分支1', '分支2'],
    },
    defaultSize: {
      width: 120,
      height: 80,
    },
    handles: [
      {
        id: 'parallel-input',
        type: 'target',
        position: 'left',
        isConnectable: true,
      },
      {
        id: 'parallel-output-1',
        type: 'source',
        position: 'top',
        isConnectable: true,
      },
      {
        id: 'parallel-output-2',
        type: 'source',
        position: 'bottom',
        isConnectable: true,
      },
    ],
  },
  {
    type: 'merge',
    name: '合并',
    description: '合并节点，等待所有分支完成',
    icon: 'merge-cells',
    color: '#13c2c2',
    defaultProperties: {
      name: '合并节点',
      description: '等待所有分支完成后继续',
    },
    defaultSize: {
      width: 100,
      height: 60,
    },
    handles: [
      {
        id: 'merge-input-1',
        type: 'target',
        position: 'top',
        isConnectable: true,
      },
      {
        id: 'merge-input-2',
        type: 'target',
        position: 'bottom',
        isConnectable: true,
      },
      {
        id: 'merge-output',
        type: 'source',
        position: 'right',
        isConnectable: true,
      },
    ],
  },
];

/**
 * 默认工作流配置
 */
export const DEFAULT_WORKFLOW_CONFIG: WorkflowConfig = {
  mode: 'edit',
  readonly: false,
  grid: {
    visible: true,
    size: 20,
    color: '#f0f0f0',
  },
  snap: {
    enabled: true,
    grid: true,
  },
  background: {
    color: '#fafafa',
    pattern: 'dots',
  },
  minimap: {
    enabled: true,
    position: 'bottom-right',
  },
  controls: {
    enabled: true,
    position: 'bottom-left',
  },
  zoom: {
    min: 0.1,
    max: 2,
    step: 0.1,
  },
};

/**
 * 条件操作符配置
 */
export const CONDITION_OPERATORS = [
  { value: 'eq', label: '等于', symbol: '=' },
  { value: 'ne', label: '不等于', symbol: '≠' },
  { value: 'gt', label: '大于', symbol: '>' },
  { value: 'gte', label: '大于等于', symbol: '≥' },
  { value: 'lt', label: '小于', symbol: '<' },
  { value: 'lte', label: '小于等于', symbol: '≤' },
  { value: 'contains', label: '包含', symbol: '∋' },
  { value: 'in', label: '在范围内', symbol: '∈' },
  { value: 'not_in', label: '不在范围内', symbol: '∉' },
];

/**
 * 审批类型配置
 */
export const APPROVAL_TYPES = [
  { value: 'single', label: '单人审批', description: '任意一人审批通过即可' },
  { value: 'all', label: '全部审批', description: '所有审批人都需要审批通过' },
  { value: 'majority', label: '多数审批', description: '超过半数审批人通过即可' },
];

/**
 * 节点状态配置
 */
export const NODE_STATUS_CONFIG = {
  pending: { color: '#faad14', label: '待处理' },
  processing: { color: '#1890ff', label: '处理中' },
  completed: { color: '#52c41a', label: '已完成' },
  rejected: { color: '#f5222d', label: '已拒绝' },
  timeout: { color: '#d9d9d9', label: '超时' },
};

/**
 * 连线类型配置
 */
export const EDGE_TYPES = [
  { value: 'straight', label: '直线' },
  { value: 'bezier', label: '贝塞尔曲线' },
  { value: 'step', label: '阶梯线' },
  { value: 'smoothstep', label: '平滑阶梯线' },
];

/**
 * 预设工作流模板
 */
export const WORKFLOW_TEMPLATES: WorkflowTemplate[] = [
  {
    id: 'simple-approval',
    name: '简单审批流程',
    description: '包含开始、审批、结束的基础流程',
    category: '基础模板',
    tags: ['简单', '审批', '基础'],
    data: {
      nodes: [
        {
          id: 'start-1',
          type: 'start',
          position: { x: 100, y: 200 },
          properties: { name: '开始' },
        },
        {
          id: 'approval-1',
          type: 'approval',
          position: { x: 300, y: 200 },
          properties: {
            name: '部门审批',
            approvers: [],
            approvalType: 'single',
            timeLimit: 24,
          },
        },
        {
          id: 'end-1',
          type: 'end',
          position: { x: 500, y: 200 },
          properties: { name: '结束' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          type: 'bezier',
          sourceNodeId: 'start-1',
          targetNodeId: 'approval-1',
          properties: { label: '提交' },
        },
        {
          id: 'edge-2',
          type: 'bezier',
          sourceNodeId: 'approval-1',
          targetNodeId: 'end-1',
          properties: { label: '通过' },
        },
      ],
    },
  },
  {
    id: 'conditional-approval',
    name: '条件审批流程',
    description: '根据条件进行不同的审批路径',
    category: '条件模板',
    tags: ['条件', '分支', '审批'],
    data: {
      nodes: [
        {
          id: 'start-1',
          type: 'start',
          position: { x: 100, y: 200 },
          properties: { name: '开始' },
        },
        {
          id: 'condition-1',
          type: 'condition',
          position: { x: 300, y: 200 },
          properties: {
            name: '金额判断',
            conditions: [
              {
                id: 'cond-1',
                field: 'amount',
                operator: 'gt',
                value: 10000,
                label: '金额 > 10000',
              },
            ],
          },
        },
        {
          id: 'approval-1',
          type: 'approval',
          position: { x: 500, y: 120 },
          properties: {
            name: '高级审批',
            approvers: [],
            approvalType: 'all',
          },
        },
        {
          id: 'approval-2',
          type: 'approval',
          position: { x: 500, y: 280 },
          properties: {
            name: '普通审批',
            approvers: [],
            approvalType: 'single',
          },
        },
        {
          id: 'end-1',
          type: 'end',
          position: { x: 700, y: 200 },
          properties: { name: '结束' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          type: 'bezier',
          sourceNodeId: 'start-1',
          targetNodeId: 'condition-1',
          properties: { label: '提交' },
        },
        {
          id: 'edge-2',
          type: 'bezier',
          sourceNodeId: 'condition-1',
          targetNodeId: 'approval-1',
          properties: { label: '是', condition: 'amount > 10000' },
        },
        {
          id: 'edge-3',
          type: 'bezier',
          sourceNodeId: 'condition-1',
          targetNodeId: 'approval-2',
          properties: { label: '否', condition: 'amount <= 10000' },
        },
        {
          id: 'edge-4',
          type: 'bezier',
          sourceNodeId: 'approval-1',
          targetNodeId: 'end-1',
          properties: { label: '通过' },
        },
        {
          id: 'edge-5',
          type: 'bezier',
          sourceNodeId: 'approval-2',
          targetNodeId: 'end-1',
          properties: { label: '通过' },
        },
      ],
    },
  },
  {
    id: 'parallel-approval',
    name: '并行审批流程',
    description: '多个部门同时审批的并行流程',
    category: '并行模板',
    tags: ['并行', '多部门', '审批'],
    data: {
      nodes: [
        {
          id: 'start-1',
          type: 'start',
          position: { x: 100, y: 200 },
          properties: { name: '开始' },
        },
        {
          id: 'parallel-1',
          type: 'parallel',
          position: { x: 300, y: 200 },
          properties: {
            name: '并行审批',
            branches: ['质控部', '生产部'],
          },
        },
        {
          id: 'approval-1',
          type: 'approval',
          position: { x: 500, y: 120 },
          properties: {
            name: '质控部审批',
            approvers: [],
            approvalType: 'single',
          },
        },
        {
          id: 'approval-2',
          type: 'approval',
          position: { x: 500, y: 280 },
          properties: {
            name: '生产部审批',
            approvers: [],
            approvalType: 'single',
          },
        },
        {
          id: 'merge-1',
          type: 'merge',
          position: { x: 700, y: 200 },
          properties: { name: '合并' },
        },
        {
          id: 'end-1',
          type: 'end',
          position: { x: 900, y: 200 },
          properties: { name: '结束' },
        },
      ],
      edges: [
        {
          id: 'edge-1',
          type: 'bezier',
          sourceNodeId: 'start-1',
          targetNodeId: 'parallel-1',
          properties: { label: '提交' },
        },
        {
          id: 'edge-2',
          type: 'bezier',
          sourceNodeId: 'parallel-1',
          targetNodeId: 'approval-1',
          properties: { label: '质控部' },
        },
        {
          id: 'edge-3',
          type: 'bezier',
          sourceNodeId: 'parallel-1',
          targetNodeId: 'approval-2',
          properties: { label: '生产部' },
        },
        {
          id: 'edge-4',
          type: 'bezier',
          sourceNodeId: 'approval-1',
          targetNodeId: 'merge-1',
          properties: { label: '通过' },
        },
        {
          id: 'edge-5',
          type: 'bezier',
          sourceNodeId: 'approval-2',
          targetNodeId: 'merge-1',
          properties: { label: '通过' },
        },
        {
          id: 'edge-6',
          type: 'bezier',
          sourceNodeId: 'merge-1',
          targetNodeId: 'end-1',
          properties: { label: '完成' },
        },
      ],
    },
  },
];

/**
 * 默认审批人数据
 */
export const DEFAULT_APPROVERS = [
  {
    id: '1',
    name: '张三',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控员',
  },
  {
    id: '2',
    name: '李四',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控主管',
  },
  {
    id: '3',
    name: '王五',
    email: '<EMAIL>',
    department: '质控部',
    role: '质控经理',
  },
  {
    id: '4',
    name: '赵六',
    email: '<EMAIL>',
    department: '生产部',
    role: '生产主管',
  },
  {
    id: '5',
    name: '钱七',
    email: '<EMAIL>',
    department: '研发部',
    role: '研发经理',
  },
];

export default {
  DEFAULT_NODE_TEMPLATES,
  DEFAULT_WORKFLOW_CONFIG,
  CONDITION_OPERATORS,
  APPROVAL_TYPES,
  NODE_STATUS_CONFIG,
  EDGE_TYPES,
  WORKFLOW_TEMPLATES,
  DEFAULT_APPROVERS,
}; 
