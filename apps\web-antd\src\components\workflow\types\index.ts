/**
 * 业务审批工作流组件类型定义
 */

// 工作流模式
export type WorkflowMode = 'edit' | 'preview';

// 节点类型
export type NodeType =
  | 'approval' // 审批节点
  | 'condition' // 条件节点
  | 'custom' // 自定义节点
  | 'end' // 结束节点
  | 'merge' // 合并节点
  | 'parallel' // 并行节点
  | 'start'; // 开始节点

// 连线类型
export type EdgeType =
  | 'bezier' // 贝塞尔曲线
  | 'smoothstep' // 平滑阶梯线
  | 'step' // 阶梯线
  | 'straight'; // 直线

// 节点状态
export type NodeStatus =
  | 'completed' // 已完成
  | 'pending' // 待处理
  | 'processing' // 处理中
  | 'rejected' // 已拒绝
  | 'timeout'; // 超时

// 审批人信息
export interface Approver {
  id: string;
  name: string;
  email?: string;
  department?: string;
  role?: string;
  avatar?: string;
}

// 条件规则
export interface ConditionRule {
  id: string;
  field: string; // 字段名
  operator:
    | 'contains'
    | 'eq'
    | 'gt'
    | 'gte'
    | 'in'
    | 'lt'
    | 'lte'
    | 'ne'
    | 'not_in';
  value: any; // 比较值
  label?: string; // 规则标签
}

// 节点位置
export interface NodePosition {
  x: number;
  y: number;
}

// 节点尺寸
export interface NodeSize {
  width: number;
  height: number;
}

// 连接点
export interface Handle {
  id: string;
  type: 'source' | 'target';
  position: 'bottom' | 'left' | 'right' | 'top';
  style?: Record<string, any>;
  isConnectable?: boolean;
}

// 节点属性
export interface NodeProperties {
  // 通用属性
  name: string;
  description?: string;

  // 审批节点属性
  approvers?: Approver[];
  approvalType?: 'all' | 'majority' | 'single'; // 审批类型：单人、全部、多数
  timeLimit?: number; // 审批时限（小时）
  autoApprove?: boolean; // 超时自动审批
  escalation?: boolean; // 是否启用升级
  escalationTime?: number; // 升级时间（小时）
  escalationApprovers?: Approver[]; // 升级审批人

  // 条件节点属性
  conditions?: ConditionRule[];
  defaultPath?: 'false' | 'true'; // 默认路径

  // 并行节点属性
  branches?: string[]; // 分支名称

  // 样式属性
  color?: string;
  backgroundColor?: string;
  borderColor?: string;

  // 自定义属性
  [key: string]: any;
}

// 工作流节点
export interface WorkflowNode {
  id: string;
  type: NodeType;
  position: NodePosition;
  size?: NodeSize;
  properties: NodeProperties;
  handles?: Handle[];
  status?: NodeStatus;
  data?: Record<string, any>;
  selected?: boolean;
  dragging?: boolean;
  resizing?: boolean;
}

// 连线属性
export interface EdgeProperties {
  label?: string;
  condition?: string; // 条件表达式
  color?: string;
  strokeWidth?: number;
  animated?: boolean;

  // 自定义属性
  [key: string]: any;
}

// 工作流连线
export interface WorkflowEdge {
  id: string;
  type: EdgeType;
  sourceNodeId: string;
  targetNodeId: string;
  sourceHandle?: string;
  targetHandle?: string;
  properties: EdgeProperties;
  selected?: boolean;
  path?: string; // SVG 路径
  markers?: {
    end?: string;
    start?: string;
  };
}

// 工作流数据
export interface WorkflowData {
  id?: string;
  name?: string;
  description?: string;
  version?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport?: {
    x: number;
    y: number;
    zoom: number;
  };
  metadata?: Record<string, any>;
}

// 验证结果
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  id: string;
  type: 'error';
  message: string;
  nodeId?: string;
  edgeId?: string;
}

export interface ValidationWarning {
  id: string;
  type: 'warning';
  message: string;
  nodeId?: string;
  edgeId?: string;
}

// 节点模板
export interface NodeTemplate {
  type: NodeType;
  name: string;
  description: string;
  icon: string;
  color: string;
  defaultProperties: Partial<NodeProperties>;
  defaultSize?: NodeSize;
  handles?: Handle[];
}

// 工作流配置
export interface WorkflowConfig {
  mode: WorkflowMode;
  readonly?: boolean;
  grid?: {
    color: string;
    size: number;
    visible: boolean;
  };
  snap?: {
    enabled: boolean;
    grid: boolean;
  };
  background?: {
    color: string;
    pattern?: 'cross' | 'dots' | 'lines';
  };
  minimap?: {
    enabled: boolean;
    position: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  };
  controls?: {
    enabled: boolean;
    position: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  };
  zoom?: {
    max: number;
    min: number;
    step: number;
  };
}

// 组件 Props
export interface WorkflowProps {
  modelValue?: WorkflowData;
  config?: Partial<WorkflowConfig>;
  nodeTemplates?: NodeTemplate[];
  approverDataSource?: Approver[];
  readonly?: boolean;
  height?: number | string;
  width?: number | string;
}

// 组件 Emits
export interface WorkflowEmits {
  'update:modelValue': [value: WorkflowData];
  'node-click': [node: WorkflowNode];
  'node-double-click': [node: WorkflowNode];
  'node-context-menu': [node: WorkflowNode, event: MouseEvent];
  'edge-click': [edge: WorkflowEdge];
  'edge-context-menu': [edge: WorkflowEdge, event: MouseEvent];
  'selection-change': [
    selection: { edges: WorkflowEdge[]; nodes: WorkflowNode[] },
  ];
  validate: [result: ValidationResult];
  'canvas-click': [event: MouseEvent];
  'canvas-context-menu': [event: MouseEvent];
}

// 工作流实例方法
export interface WorkflowInstance {
  // 数据操作
  getWorkflowData(): WorkflowData;
  setWorkflowData(data: WorkflowData): void;

  // 节点操作
  addNode(node: Omit<WorkflowNode, 'id'>): string;
  updateNode(id: string, updates: Partial<WorkflowNode>): void;
  deleteNode(id: string): void;
  getNode(id: string): undefined | WorkflowNode;

  // 连线操作
  addEdge(edge: Omit<WorkflowEdge, 'id'>): string;
  updateEdge(id: string, updates: Partial<WorkflowEdge>): void;
  deleteEdge(id: string): void;
  getEdge(id: string): undefined | WorkflowEdge;

  // 选择操作
  selectNode(id: string): void;
  selectEdge(id: string): void;
  selectAll(): void;
  clearSelection(): void;
  getSelection(): { edges: WorkflowEdge[]; nodes: WorkflowNode[] };

  // 视图操作
  zoomIn(): void;
  zoomOut(): void;
  zoomToFit(): void;
  zoomToSelection(): void;
  centerView(): void;

  // 验证
  validate(): ValidationResult;

  // 数据导入导出
  exportData(format?: 'json' | 'xml'): string;
  importData(data: string | WorkflowData): boolean;

  // 历史操作
  undo(): boolean;
  redo(): boolean;
  canUndo(): boolean;
  canRedo(): boolean;
}

// 事件类型
export interface WorkflowEvents {
  'node:add': { node: WorkflowNode };
  'node:update': { changes: Partial<WorkflowNode>; node: WorkflowNode };
  'node:delete': { nodeId: string };
  'edge:add': { edge: WorkflowEdge };
  'edge:update': { changes: Partial<WorkflowEdge>; edge: WorkflowEdge };
  'edge:delete': { edgeId: string };
  'selection:change': {
    selection: { edges: WorkflowEdge[]; nodes: WorkflowNode[] };
  };
  'viewport:change': { viewport: { x: number; y: number; zoom: number } };
  validate: { result: ValidationResult };
}

// 工具栏动作
export interface ToolbarAction {
  id: string;
  label: string;
  icon: string;
  disabled?: boolean;
  tooltip?: string;
  handler: () => void;
}

// 工作流模板
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail?: string;
  data: WorkflowData;
  tags?: string[];
}

export default {};
