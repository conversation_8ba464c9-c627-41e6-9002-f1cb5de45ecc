<template>
  <div class="edge-property-form">
    <Form
      :model="formData"
      layout="vertical"
      size="small"
      @finish="handleSubmit"
    >
      <!-- 基础属性 -->
      <Divider orientation="left">基础属性</Divider>
      
      <Form.Item label="连线标签" name="label">
        <Input
          v-model:value="formData.label"
          placeholder="请输入连线标签"
          @change="handleChange"
        />
      </Form.Item>

      <Form.Item label="条件表达式" name="condition">
        <Textarea
          v-model:value="formData.condition"
          placeholder="请输入条件表达式（可选）"
          :rows="2"
          @change="handleChange"
        />
      </Form.Item>

      <!-- 样式设置 -->
      <Divider orientation="left">样式设置</Divider>

      <Form.Item label="连线类型" name="type">
        <Select
          v-model:value="formData.type"
          placeholder="请选择连线类型"
          :options="edgeTypeOptions"
          @change="handleChange"
        />
      </Form.Item>

      <Form.Item label="连线颜色" name="color">
        <div class="color-picker">
          <Input
            v-model:value="formData.color"
            placeholder="#8c8c8c"
            @change="handleChange"
          />
          <div
            class="color-preview"
            :style="{ backgroundColor: formData.color || '#8c8c8c' }"
          />
        </div>
      </Form.Item>

      <Form.Item label="线条粗细" name="strokeWidth">
        <Slider
          v-model:value="formData.strokeWidth"
          :min="1"
          :max="10"
          :marks="{ 1: '1px', 5: '5px', 10: '10px' }"
          @change="handleChange"
        />
      </Form.Item>

      <Form.Item name="animated">
        <Checkbox
          v-model:checked="formData.animated"
          @change="handleChange"
        >
          启用动画效果
        </Checkbox>
      </Form.Item>

      <Form.Item name="dashed">
        <Checkbox
          v-model:checked="formData.dashed"
          @change="handleChange"
        >
          虚线样式
        </Checkbox>
      </Form.Item>

      <!-- 连线信息 -->
      <Divider orientation="left">连线信息</Divider>

      <div class="edge-info">
        <div class="info-item">
          <span class="info-label">源节点:</span>
          <span class="info-value">{{ sourceNodeName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">目标节点:</span>
          <span class="info-value">{{ targetNodeName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">连线ID:</span>
          <span class="info-value">{{ edge.id }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <Button type="primary" block @click="handleSubmit">
          保存更改
        </Button>
        <Button block style="margin-top: 8px" @click="handleReset">
          重置
        </Button>
        <Button
          block
          danger
          style="margin-top: 8px"
          @click="handleDelete"
        >
          <Trash2 :size="16" />
          删除连线
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, watch, inject } from 'vue';
import {
  Form,
  Input,
  Textarea,
  Select,
  Slider,
  Checkbox,
  Button,
  Divider,
  message,
} from 'ant-design-vue';
import { Trash2 } from 'lucide-vue-next';
import type { WorkflowEdge, WorkflowNode } from '../../types';
import { EDGE_TYPES } from '../../config';

interface Props {
  edge: WorkflowEdge;
}

interface Emits {
  (e: 'update', updates: Partial<WorkflowEdge>): void;
  (e: 'delete'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 注入工作流节点数据（用于显示节点名称）
const workflowNodes = inject<WorkflowNode[]>('workflowNodes', []);

// 表单数据
const formData = reactive({
  type: 'bezier' as 'straight' | 'bezier' | 'step' | 'smoothstep',
  label: '',
  condition: '',
  color: '',
  strokeWidth: 2,
  animated: false,
  dashed: false,
});

// 连线类型选项
const edgeTypeOptions = computed(() => {
  return EDGE_TYPES.map(type => ({
    label: type.label,
    value: type.value,
  }));
});

// 源节点名称
const sourceNodeName = computed(() => {
  const sourceNode = workflowNodes.find(node => node.id === props.edge.sourceNodeId);
  return sourceNode?.properties.name || sourceNode?.id || '未知节点';
});

// 目标节点名称
const targetNodeName = computed(() => {
  const targetNode = workflowNodes.find(node => node.id === props.edge.targetNodeId);
  return targetNode?.properties.name || targetNode?.id || '未知节点';
});

// 初始化表单数据
function initFormData() {
  const { properties } = props.edge;
  
  Object.assign(formData, {
    type: props.edge.type || 'bezier',
    label: properties.label || '',
    condition: properties.condition || '',
    color: properties.color || '',
    strokeWidth: properties.strokeWidth || 2,
    animated: properties.animated || false,
    dashed: properties.dashed || false,
  });
}

// 监听连线变化
watch(
  () => props.edge,
  () => {
    initFormData();
  },
  { immediate: true }
);

// 处理变更
function handleChange() {
  // 实时更新
  const updates = buildUpdates();
  emit('update', updates);
}

// 构建更新数据
function buildUpdates(): Partial<WorkflowEdge> {
  return {
    type: formData.type,
    properties: {
      label: formData.label,
      condition: formData.condition,
      color: formData.color,
      strokeWidth: formData.strokeWidth,
      animated: formData.animated,
      dashed: formData.dashed,
    },
  };
}

// 提交表单
function handleSubmit() {
  const updates = buildUpdates();
  emit('update', updates);
  message.success('连线属性已保存');
}

// 重置表单
function handleReset() {
  initFormData();
  message.info('连线属性已重置');
}

// 删除连线
function handleDelete() {
  emit('delete');
}
</script>

<style scoped>
.edge-property-form {
  /* Form 组件样式由 Ant Design Vue 管理 */
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  align-items: center;
}

.color-picker .ant-input {
  flex: 1;
  margin-right: 8px;
}

.color-preview {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

/* 连线信息 */
.edge-info {
  padding: 12px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #8c8c8c;
}

.info-value {
  font-weight: 500;
  color: #262626;
}

/* 操作按钮 */
.form-actions {
  padding-top: 16px;
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
}
</style> 
